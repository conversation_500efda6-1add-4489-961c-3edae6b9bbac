"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/app/admin-dashboard/blog/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/admin-dashboard/blog/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_admin_blog_blog_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/admin/blog/blog-manager */ \"(app-pages-browser)/./src/components/admin/blog/blog-manager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst blogConfig = {\n    title: 'Blog Posts',\n    description: 'Create, edit, and manage your blog content',\n    endpoint: 'blog',\n    // Table columns configuration\n    columns: [\n        {\n            key: 'title',\n            label: 'Title',\n            sortable: true,\n            searchable: true,\n            width: '30%'\n        },\n        {\n            key: 'excerpt',\n            label: 'Excerpt',\n            sortable: false,\n            searchable: true,\n            width: '25%'\n        },\n        {\n            key: 'isPublished',\n            label: 'Status',\n            sortable: true,\n            searchable: false,\n            width: '10%'\n        },\n        {\n            key: 'categories',\n            label: 'Category',\n            sortable: true,\n            searchable: true,\n            width: '15%'\n        },\n        {\n            key: 'tags',\n            label: 'Tags',\n            sortable: false,\n            searchable: true,\n            width: '15%'\n        },\n        {\n            key: 'updatedAt',\n            label: 'Last Updated',\n            sortable: true,\n            searchable: false,\n            width: '15%'\n        }\n    ],\n    // Filters configuration\n    filters: [\n        {\n            key: 'isPublished',\n            label: 'Publication Status',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Posts'\n                },\n                {\n                    value: 'true',\n                    label: 'Published'\n                },\n                {\n                    value: 'false',\n                    label: 'Draft'\n                }\n            ]\n        },\n        {\n            key: 'categories',\n            label: 'Category',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Categories'\n                },\n                {\n                    value: 'Web Development',\n                    label: 'Web Development'\n                },\n                {\n                    value: 'Mobile Development',\n                    label: 'Mobile Development'\n                },\n                {\n                    value: 'Cloud Computing',\n                    label: 'Cloud Computing'\n                },\n                {\n                    value: 'Programming',\n                    label: 'Programming'\n                },\n                {\n                    value: 'Security',\n                    label: 'Security'\n                },\n                {\n                    value: 'AI/ML',\n                    label: 'AI/ML'\n                }\n            ]\n        }\n    ],\n    // Bulk actions configuration\n    bulkActions: [\n        {\n            action: 'publish',\n            label: 'Publish',\n            icon: 'PowerIcon',\n            variant: 'primary',\n            confirmationMessage: 'Are you sure you want to publish the selected blog posts?'\n        },\n        {\n            action: 'unpublish',\n            label: 'Unpublish',\n            icon: 'PowerIcon',\n            variant: 'secondary',\n            confirmationMessage: 'Are you sure you want to unpublish the selected blog posts?'\n        },\n        {\n            action: 'delete',\n            label: 'Delete',\n            icon: 'TrashIcon',\n            variant: 'danger',\n            confirmationMessage: 'Are you sure you want to delete the selected blog posts? This action cannot be undone.'\n        }\n    ],\n    // Action buttons for each row\n    actions: [\n        {\n            action: 'view',\n            label: 'View',\n            icon: 'EyeIcon',\n            variant: 'secondary',\n            tooltip: 'View blog post details'\n        },\n        {\n            action: 'edit',\n            label: 'Edit',\n            icon: 'PencilIcon',\n            variant: 'primary',\n            tooltip: 'Edit blog post'\n        },\n        {\n            action: 'toggle-published',\n            label: 'Toggle Published',\n            icon: 'PowerIcon',\n            variant: 'warning',\n            tooltip: 'Publish/Unpublish blog post'\n        },\n        {\n            action: 'delete',\n            label: 'Delete',\n            icon: 'TrashIcon',\n            variant: 'danger',\n            tooltip: 'Delete blog post'\n        }\n    ],\n    fields: [\n        {\n            key: 'title',\n            label: 'Title',\n            type: 'text',\n            required: true,\n            searchable: true,\n            placeholder: 'e.g., How to Build Amazing Web Applications'\n        },\n        {\n            key: 'slug',\n            label: 'Slug',\n            type: 'text',\n            required: true,\n            searchable: true,\n            placeholder: 'e.g., how-to-build-amazing-web-applications'\n        },\n        {\n            key: 'excerpt',\n            label: 'Excerpt',\n            type: 'textarea',\n            searchable: true,\n            placeholder: 'Brief description of the blog post...',\n            rows: 3\n        },\n        {\n            key: 'content',\n            label: 'Content',\n            type: 'textarea',\n            required: true,\n            searchable: true,\n            placeholder: 'Write your blog post content here...',\n            rows: 8\n        },\n        {\n            key: 'featuredImageUrl',\n            label: 'Featured Image',\n            type: 'url',\n            searchable: false,\n            placeholder: 'Enter image URL or click Upload to select file'\n        },\n        {\n            key: 'authorId',\n            label: 'Author ID',\n            type: 'text',\n            searchable: false,\n            placeholder: 'e.g., author-123'\n        },\n        {\n            key: 'categories',\n            label: 'Categories',\n            type: 'text',\n            searchable: true,\n            placeholder: 'e.g., Web Development, Technology'\n        },\n        {\n            key: 'tags',\n            label: 'Tags',\n            type: 'text',\n            searchable: true,\n            placeholder: 'e.g., react, nextjs, javascript, tutorial'\n        },\n        {\n            key: 'isPublished',\n            label: 'Published',\n            type: 'boolean',\n            defaultValue: false,\n            searchable: false\n        },\n        {\n            key: 'publishedAt',\n            label: 'Published Date',\n            type: 'datetime-local',\n            searchable: false\n        }\n    ],\n    permissions: {\n        create: true,\n        read: true,\n        update: true,\n        delete: true,\n        export: true\n    },\n    searchPlaceholder: 'Search blog posts by title, content, excerpt, categories, tags...',\n    defaultSort: {\n        field: 'updatedAt',\n        direction: 'desc'\n    },\n    pageSize: 10,\n    enableSearch: true,\n    enableFilters: true,\n    enableBulkActions: true,\n    enableExport: true,\n    enableViewControls: true,\n    enableDensityControls: true,\n    enableColumnVisibility: true,\n    defaultViewSettings: {\n        mode: 'list',\n        density: 'comfortable',\n        visibleColumns: [\n            'title',\n            'excerpt',\n            'isPublished',\n            'categories',\n            'tags',\n            'updatedAt'\n        ]\n    },\n    // Form layout configuration\n    formLayout: {\n        type: 'compact',\n        columns: 2,\n        sections: [\n            {\n                title: 'Basic Information',\n                fields: [\n                    'title',\n                    'slug',\n                    'authorId',\n                    'publishedAt'\n                ]\n            },\n            {\n                title: 'Featured Image',\n                fields: [\n                    'featuredImageUrl'\n                ]\n            },\n            {\n                title: 'Content',\n                fields: [\n                    'excerpt',\n                    'content'\n                ]\n            },\n            {\n                title: 'Categories & Tags',\n                fields: [\n                    'categories',\n                    'tags'\n                ]\n            },\n            {\n                title: 'Publishing Settings',\n                fields: [\n                    'isPublished'\n                ]\n            }\n        ]\n    }\n};\nfunction BlogPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"admin-page\",\n        \"data-section\": \"blog\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_blog_blog_manager__WEBPACK_IMPORTED_MODULE_1__.BlogManagerNew, {\n            config: blogConfig\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n            lineNumber: 296,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-dashboard/blog/page.tsx\n"));

/***/ })

});