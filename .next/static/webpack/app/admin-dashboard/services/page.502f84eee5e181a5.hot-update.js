"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/category-management.tsx":
/*!***************************************************************!*\
  !*** ./src/components/admin/services/category-management.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryManagement: () => (/* binding */ CategoryManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _category_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./category-header */ \"(app-pages-browser)/./src/components/admin/services/category-header.tsx\");\n/* __next_internal_client_entry_do_not_use__ CategoryManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Function to get category-specific icons based on category name/type\nconst getCategoryIcon = (category)=>{\n    const name = category.name.toLowerCase();\n    // Web Development related\n    if (name.includes('web') || name.includes('website') || name.includes('frontend') || name.includes('backend')) {\n        return 'fa-globe text-blue-500';\n    }\n    // Mobile Development\n    if (name.includes('mobile') || name.includes('app') || name.includes('ios') || name.includes('android')) {\n        return 'fa-mobile-alt text-green-500';\n    }\n    // Design related\n    if (name.includes('design') || name.includes('ui') || name.includes('ux') || name.includes('graphic')) {\n        return 'fa-palette text-purple-500';\n    }\n    // E-commerce\n    if (name.includes('ecommerce') || name.includes('shop') || name.includes('store') || name.includes('commerce')) {\n        return 'fa-shopping-cart text-orange-500';\n    }\n    // Marketing\n    if (name.includes('marketing') || name.includes('seo') || name.includes('social') || name.includes('advertising')) {\n        return 'fa-bullhorn text-red-500';\n    }\n    // Consulting\n    if (name.includes('consulting') || name.includes('strategy') || name.includes('business')) {\n        return 'fa-handshake text-indigo-500';\n    }\n    // Support/Maintenance\n    if (name.includes('support') || name.includes('maintenance') || name.includes('hosting')) {\n        return 'fa-tools text-gray-500';\n    }\n    // Security\n    if (name.includes('security') || name.includes('ssl') || name.includes('backup')) {\n        return 'fa-shield-alt text-yellow-500';\n    }\n    // Analytics\n    if (name.includes('analytics') || name.includes('tracking') || name.includes('report')) {\n        return 'fa-chart-line text-teal-500';\n    }\n    // Default icons\n    if (category.parentId) {\n        return 'fa-tag text-orange-500' // Subcategory\n        ;\n    }\n    return 'fa-layer-group text-blue-500' // Parent category\n    ;\n};\nfunction CategoryManagement(param) {\n    let { selectedCategory, onCategorySelect } = param;\n    var _this = this;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCategory, setEditingCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentFilters, setCurrentFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('comfortable');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        parentId: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    const filters = [\n        {\n            key: 'status',\n            label: 'Status',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Status'\n                },\n                {\n                    value: 'active',\n                    label: 'Active'\n                },\n                {\n                    value: 'inactive',\n                    label: 'Inactive'\n                }\n            ]\n        },\n        {\n            key: 'parent',\n            label: 'Parent Category',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Categories'\n                },\n                {\n                    value: 'root',\n                    label: 'Root Categories'\n                },\n                {\n                    value: 'sub',\n                    label: 'Sub Categories'\n                }\n            ]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            fetchCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            filterAndSortCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], [\n        categories,\n        searchQuery,\n        currentFilters\n    ]);\n    const filterAndSortCategories = ()=>{\n        let filtered = [\n            ...categories\n        ];\n        // Apply search filter\n        if (searchQuery.trim()) {\n            const searchLower = searchQuery.toLowerCase();\n            filtered = filtered.filter((category)=>category.name.toLowerCase().includes(searchLower) || category.description && category.description.toLowerCase().includes(searchLower));\n        }\n        // Apply status filter\n        if (currentFilters.status) {\n            if (currentFilters.status === 'active') {\n                filtered = filtered.filter((category)=>category.isActive);\n            } else if (currentFilters.status === 'inactive') {\n                filtered = filtered.filter((category)=>!category.isActive);\n            }\n        }\n        // Apply parent filter\n        if (currentFilters.parent) {\n            if (currentFilters.parent === 'root') {\n                filtered = filtered.filter((category)=>!category.parentId);\n            } else if (currentFilters.parent === 'sub') {\n                filtered = filtered.filter((category)=>category.parentId);\n            }\n        }\n        setFilteredCategories(filtered);\n    };\n    const buildCategoryTree = (flatCategories)=>{\n        const categoryMap = new Map();\n        const rootCategories = [];\n        // Transform and create map\n        flatCategories.forEach((cat)=>{\n            const category = {\n                id: String(cat.id),\n                name: cat.categname || cat.name,\n                description: cat.categdesc || cat.description,\n                parentId: cat.parentid ? String(cat.parentid) : undefined,\n                isActive: cat.isactive,\n                displayOrder: cat.displayorder || 0,\n                children: [],\n                _count: cat._count\n            };\n            categoryMap.set(category.id, category);\n        });\n        // Build tree structure\n        categoryMap.forEach((category)=>{\n            if (category.parentId && categoryMap.has(category.parentId)) {\n                categoryMap.get(category.parentId).children.push(category);\n            } else {\n                rootCategories.push(category);\n            }\n        });\n        // Sort by display order\n        const sortCategories = (cats)=>{\n            cats.sort((a, b)=>a.displayOrder - b.displayOrder);\n            cats.forEach((cat)=>{\n                if (cat.children) {\n                    sortCategories(cat.children);\n                }\n            });\n        };\n        sortCategories(rootCategories);\n        return rootCategories;\n    };\n    const fetchCategories = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/admin/categories?limit=100');\n            if (response.ok) {\n                const data = await response.json();\n                const categoriesData = data.data || data.categories || [];\n                setCategories(buildCategoryTree(categoriesData));\n            } else {\n                console.error('Failed to fetch categories:', response.status, response.statusText);\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEdit = (category)=>{\n        setEditingCategory(category);\n        setFormData({\n            name: category.name,\n            description: category.description || '',\n            parentId: category.parentId || '',\n            isActive: category.isActive,\n            displayOrder: category.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDelete = async (category)=>{\n        if (!confirm('Are you sure you want to delete \"'.concat(category.name, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchCategories();\n                if ((selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id) {\n                    onCategorySelect(null);\n                }\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to delete category');\n            }\n        } catch (error) {\n            console.error('Error deleting category:', error);\n            alert('An error occurred while deleting the category');\n        }\n    };\n    const handleToggleActive = async (category)=>{\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    categname: category.name,\n                    categdesc: category.description,\n                    parentid: category.parentId ? Number(category.parentId) : 0,\n                    isactive: !category.isActive,\n                    displayorder: category.displayOrder\n                })\n            });\n            if (response.ok) {\n                fetchCategories();\n            }\n        } catch (error) {\n            console.error('Error toggling category status:', error);\n        }\n    };\n    const toggleExpanded = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const renderCategory = function(category) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _categories_find, _category_children;\n        const isExpanded = expandedCategories.has(category.id);\n        const hasChildren = category.children && category.children.length > 0;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"select-none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-2 rounded-none cursor-pointer transition-colors border border-gray-200 \".concat(isSelected ? 'bg-blue-50 border-blue-300' : 'bg-white hover:bg-gray-50'),\n                    onClick: ()=>onCategorySelect(category),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-4\",\n                            style: {\n                                marginLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    toggleExpanded(category.id);\n                                },\n                                className: \"p-0.5 hover:bg-gray-200 rounded-none\",\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 19\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 19\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-full flex items-center justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas \".concat(getCategoryIcon(category), \" text-3xl\")\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0 flex flex-col justify-center ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold text-base truncate \".concat(isSelected ? 'text-blue-900' : 'text-gray-900'),\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, _this),\n                                category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm truncate mt-0.5 \".concat(isSelected ? 'text-blue-600' : 'text-gray-600'),\n                                    children: category.description\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-32 flex items-center\",\n                            children: category.parentId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600 truncate block\",\n                                children: ((_categories_find = categories.find((c)=>c.id === category.parentId)) === null || _categories_find === void 0 ? void 0 : _categories_find.name) || 'Unknown'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400 italic\",\n                                children: \"Root\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 flex items-center\",\n                            children: category._count && typeof category._count.services === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 px-2 py-0.5 rounded-none text-sm font-medium\",\n                                children: category._count.services\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                children: category.isActive ? 'Active' : 'Inactive'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 w-32\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleEdit(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    title: \"Edit category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleToggleActive(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white \".concat(category.isActive ? 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                    title: category.isActive ? 'Deactivate category' : 'Activate category',\n                                    children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 23\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 23\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleDelete(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                    title: \"Delete category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 19\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, _this),\n                hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-6\",\n                    children: (_category_children = category.children) === null || _category_children === void 0 ? void 0 : _category_children.map((child)=>renderCategory(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, category.id, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, _this);\n    };\n    const handleCreateClick = ()=>{\n        setIsFormOpen(true);\n        setEditingCategory(null);\n        setFormData({\n            name: '',\n            description: '',\n            parentId: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleFiltersChange = (newFilters)=>{\n        setCurrentFilters(newFilters);\n    };\n    const renderCategoryCard = function(category) {\n        let isLargeCard = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        var _category_children;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        const hasChildren = category.children && category.children.length > 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border rounded-none cursor-pointer transition-all duration-200 \".concat(isSelected ? 'border-blue-500 shadow-md bg-blue-50' : 'border-gray-200 hover:border-gray-300 hover:shadow-sm hover:bg-gray-100', \" \").concat(isLargeCard ? 'p-6' : 'p-4'),\n            onClick: ()=>onCategorySelect(category),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold \".concat(isSelected ? 'text-blue-900' : 'text-gray-900'),\n                                        children: category.name\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'),\n                                        children: category.isActive ? 'Active' : 'Inactive'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, _this),\n                            category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mb-3 \".concat(isSelected ? 'text-blue-600' : 'text-gray-600'),\n                                children: category.description\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 15\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-xs text-gray-500\",\n                                children: [\n                                    category._count && typeof category._count.services === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            category._count.services,\n                                            \" services\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 17\n                                    }, _this),\n                                    hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            (_category_children = category.children) === null || _category_children === void 0 ? void 0 : _category_children.length,\n                                            \" subcategories\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Order: \",\n                                            category.displayOrder\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleEdit(category);\n                                },\n                                className: \"text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded p-1\",\n                                title: \"Edit category\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleToggleActive(category);\n                                },\n                                className: \"rounded transition-colors p-1 \".concat(category.isActive ? 'text-green-600 hover:text-green-700 hover:bg-green-50' : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'),\n                                title: category.isActive ? 'Deactivate category' : 'Activate category',\n                                children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 17\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleDelete(category);\n                                },\n                                className: \"text-gray-400 hover:text-red-600 hover:bg-red-50 rounded p-1\",\n                                title: \"Delete category\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 474,\n                columnNumber: 9\n            }, _this)\n        }, category.id, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 465,\n            columnNumber: 7\n        }, _this);\n    };\n    const getAllCategories = (cats)=>{\n        let all = [];\n        cats.forEach((cat)=>{\n            all.push(cat);\n            if (cat.children && cat.children.length > 0) {\n                all = all.concat(getAllCategories(cat.children));\n            }\n        });\n        return all;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_category_header__WEBPACK_IMPORTED_MODULE_2__.CategoryHeader, {\n                title: \"Categories\",\n                description: \"Manage service categories and subcategories\",\n                searchPlaceholder: \"Search categories by name or description...\",\n                searchQuery: searchQuery,\n                onSearchChange: setSearchQuery,\n                enableSearch: true,\n                enableFilters: true,\n                enableViewControls: true,\n                enableCreate: true,\n                onCreateClick: handleCreateClick,\n                createButtonText: \"Add Category\",\n                viewMode: viewMode,\n                onViewModeChange: setViewMode,\n                filters: filters,\n                onFiltersChange: handleFiltersChange,\n                currentFilters: currentFilters,\n                itemCount: filteredCategories.length,\n                totalItems: categories.length\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 571,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-300 rounded-lg shadow-sm border border-gray-400\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Loading categories...\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 597,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 595,\n                    columnNumber: 11\n                }, this) : filteredCategories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No categories found\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: searchQuery || Object.keys(currentFilters).some((key)=>currentFilters[key]) ? 'Try adjusting your search terms or filters.' : 'Get started by creating your first category.'\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCreateClick,\n                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Category\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-300 rounded-none border border-gray-400 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-3 py-2 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: getAllCategories(filteredCategories).map((category)=>{\n                                            var _categories_find, _category__count;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.tr, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 10\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                onClick: ()=>onCategorySelect(category),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-3 py-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"fas \".concat(getCategoryIcon(category), \" text-sm\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-bold text-gray-900 truncate leading-tight\",\n                                                                        children: category.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 657,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-3 py-1\",\n                                                        children: category.parentId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-600 leading-tight\",\n                                                            children: ((_categories_find = categories.find((c)=>c.id === category.parentId)) === null || _categories_find === void 0 ? void 0 : _categories_find.name) || 'Unknown'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400 italic leading-tight\",\n                                                            children: \"Root\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-3 py-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                                            children: ((_category__count = category._count) === null || _category__count === void 0 ? void 0 : _category__count.services) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-3 py-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                            children: category.isActive ? 'Active' : 'Inactive'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-3 py-1 text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleEdit(category);\n                                                                    },\n                                                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                                    title: \"Edit Category\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleToggleActive(category);\n                                                                    },\n                                                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(category.isActive ? 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                                    title: category.isActive ? 'Deactivate Category' : 'Activate Category',\n                                                                    children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleDelete(category);\n                                                                    },\n                                                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                                    title: \"Delete Category\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, category.id, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-400 border border-gray-500 rounded-none px-4 py-2 mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: filteredCategories.map((category)=>renderCategory(category))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n                            children: getAllCategories(filteredCategories).map((category)=>renderCategoryCard(category, false))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 766,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'card' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3\",\n                            children: getAllCategories(filteredCategories).map((category)=>renderCategoryCard(category, true))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 772,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 617,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 593,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: editingCategory ? 'Edit Category' : 'Add Category'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 797,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: async (e)=>{\n                                    e.preventDefault();\n                                    try {\n                                        const url = editingCategory ? \"/api/admin/categories/\".concat(editingCategory.id) : '/api/admin/categories';\n                                        const method = editingCategory ? 'PUT' : 'POST';\n                                        const response = await fetch(url, {\n                                            method,\n                                            headers: {\n                                                'Content-Type': 'application/json'\n                                            },\n                                            body: JSON.stringify({\n                                                categname: formData.name,\n                                                categdesc: formData.description,\n                                                parentid: formData.parentId ? Number(formData.parentId) : 0,\n                                                isactive: formData.isActive,\n                                                displayorder: formData.displayOrder\n                                            })\n                                        });\n                                        if (response.ok) {\n                                            setIsFormOpen(false);\n                                            fetchCategories();\n                                        } else {\n                                            const errorData = await response.json();\n                                            alert(errorData.message || 'Failed to save category');\n                                        }\n                                    } catch (error) {\n                                        console.error('Error saving category:', error);\n                                        alert('An error occurred while saving the category');\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        required: true,\n                                                        value: formData.name,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                name: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                description: e.target.value\n                                                            }),\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Parent Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.parentId,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                parentId: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"No parent (root category)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            categories.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: cat.id,\n                                                                    children: cat.name\n                                                                }, cat.id, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: formData.isActive,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    isActive: e.target.checked\n                                                                }),\n                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 881,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700\",\n                                                            children: \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 879,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Display Order\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.displayOrder,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                displayOrder: Number(e.target.value)\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: editingCategory ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 801,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 790,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 783,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 781,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n        lineNumber: 570,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoryManagement, \"Tq8luWYvmcK5f6HCbPVBjHxoERU=\");\n_c = CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/category-management.tsx\n"));

/***/ })

});