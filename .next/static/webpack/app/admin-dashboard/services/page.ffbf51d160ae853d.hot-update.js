"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/option-features-management.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/admin/services/option-features-management.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionFeaturesManagement: () => (/* binding */ OptionFeaturesManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OptionFeaturesManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OptionFeaturesManagement(param) {\n    let { option } = param;\n    _s();\n    const [features, setFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredFeatures, setFilteredFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFeature, setEditingFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    // Function to get feature-specific icons based on feature name/type\n    const getFeatureIcon = (feature)=>{\n        const name = feature.name.toLowerCase();\n        // Design/UI features\n        if (name.includes('design') || name.includes('template') || name.includes('theme') || name.includes('layout')) {\n            return 'fa-palette text-purple-500';\n        }\n        // Content/Pages features\n        if (name.includes('page') || name.includes('content') || name.includes('blog') || name.includes('article')) {\n            return 'fa-file-alt text-blue-500';\n        }\n        // E-commerce features\n        if (name.includes('product') || name.includes('cart') || name.includes('payment') || name.includes('checkout')) {\n            return 'fa-shopping-cart text-orange-500';\n        }\n        // SEO/Marketing features\n        if (name.includes('seo') || name.includes('meta') || name.includes('analytics') || name.includes('tracking')) {\n            return 'fa-search text-green-500';\n        }\n        // Security features\n        if (name.includes('ssl') || name.includes('security') || name.includes('backup') || name.includes('protection')) {\n            return 'fa-shield-alt text-red-500';\n        }\n        // Performance features\n        if (name.includes('speed') || name.includes('cache') || name.includes('optimization') || name.includes('performance')) {\n            return 'fa-tachometer-alt text-yellow-500';\n        }\n        // Integration features\n        if (name.includes('integration') || name.includes('api') || name.includes('plugin') || name.includes('extension')) {\n            return 'fa-plug text-indigo-500';\n        }\n        // Support features\n        if (name.includes('support') || name.includes('help') || name.includes('documentation') || name.includes('training')) {\n            return 'fa-life-ring text-teal-500';\n        }\n        // Mobile features\n        if (name.includes('mobile') || name.includes('responsive') || name.includes('app')) {\n            return 'fa-mobile-alt text-pink-500';\n        }\n        // Social features\n        if (name.includes('social') || name.includes('share') || name.includes('facebook') || name.includes('twitter')) {\n            return 'fa-share-alt text-blue-400';\n        }\n        // Database/Storage features\n        if (name.includes('database') || name.includes('storage') || name.includes('hosting') || name.includes('server')) {\n            return 'fa-database text-gray-500';\n        }\n        // Default based on status\n        if (feature.isHighlighted) {\n            return 'fa-star text-yellow-400';\n        }\n        if (feature.isIncluded) {\n            return 'fa-check-circle text-green-500';\n        }\n        return 'fa-circle text-gray-400';\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        isIncluded: true,\n        isHighlighted: false,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"OptionFeaturesManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"OptionFeaturesManagement.useEffect.timer\"], 300);\n            return ({\n                \"OptionFeaturesManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"OptionFeaturesManagement.useEffect\"];\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            fetchFeatures();\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        option.id\n    ]);\n    // Mock data for demonstration\n    const fetchFeatures = async ()=>{\n        setLoading(true);\n        try {\n            // Mock data - replace with actual API call\n            const mockFeatures = [\n                {\n                    id: '1',\n                    optionId: option.id,\n                    name: 'Responsive Design',\n                    description: 'Mobile-friendly design that works on all devices',\n                    isIncluded: true,\n                    isHighlighted: true,\n                    displayOrder: 1,\n                    createdAt: '2024-01-15T10:00:00Z',\n                    updatedAt: '2024-01-15T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '2',\n                    optionId: option.id,\n                    name: 'SEO Optimization',\n                    description: 'Search engine optimization for better visibility',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 2,\n                    createdAt: '2024-01-14T10:00:00Z',\n                    updatedAt: '2024-01-14T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '3',\n                    optionId: option.id,\n                    name: 'Analytics Integration',\n                    description: 'Google Analytics and tracking setup',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 3,\n                    createdAt: '2024-01-13T10:00:00Z',\n                    updatedAt: '2024-01-13T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '4',\n                    optionId: option.id,\n                    name: 'Premium Support',\n                    description: '24/7 priority customer support',\n                    isIncluded: false,\n                    isHighlighted: true,\n                    displayOrder: 4,\n                    createdAt: '2024-01-12T10:00:00Z',\n                    updatedAt: '2024-01-12T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                }\n            ];\n            setFeatures(mockFeatures);\n            setFilteredFeatures(mockFeatures);\n        } catch (error) {\n            console.error('Error fetching features:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateFeature = ()=>{\n        setIsFormOpen(true);\n        setEditingFeature(null);\n        setFormData({\n            name: '',\n            description: '',\n            isIncluded: true,\n            isHighlighted: false,\n            displayOrder: 0\n        });\n    };\n    const handleEditFeature = (feature)=>{\n        setEditingFeature(feature);\n        setFormData({\n            name: feature.name,\n            description: feature.description || '',\n            isIncluded: feature.isIncluded,\n            isHighlighted: feature.isHighlighted,\n            displayOrder: feature.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteFeature = async (featureId)=>{\n        if (confirm('Are you sure you want to delete this feature?')) {\n            try {\n                // Mock delete - replace with actual API call\n                setFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n                setFilteredFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n            } catch (error) {\n                console.error('Error deleting feature:', error);\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            if (editingFeature) {\n                // Update existing feature\n                const updatedFeature = {\n                    ...editingFeature,\n                    ...formData\n                };\n                setFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n                setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n            } else {\n                // Create new feature\n                const newFeature = {\n                    id: Date.now().toString(),\n                    optionId: option.id,\n                    ...formData,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                };\n                setFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n                setFilteredFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n            }\n            setIsFormOpen(false);\n            setEditingFeature(null);\n        } catch (error) {\n            console.error('Error saving feature:', error);\n        }\n    };\n    const toggleFeatureIncluded = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature:', error);\n        }\n    };\n    const toggleFeatureHighlighted = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature highlight:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Option Features\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage features for \",\n                                    option.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateFeature,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Feature\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search features...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-200 border border-gray-300 rounded-none px-4 py-2 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-40\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: filteredFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex items-center px-3 rounded-none transition-all duration-200 border border-gray-200 \".concat(feature.isHighlighted ? 'bg-purple-50 border-purple-300' : feature.isIncluded ? 'bg-green-50 border-green-300' : 'bg-white hover:bg-gray-50'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-full flex items-center justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas \".concat(getFeatureIcon(feature), \" text-3xl\")\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0 flex flex-col justify-center ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-base truncate \".concat(feature.isHighlighted ? 'text-purple-900' : feature.isIncluded ? 'text-green-900' : 'text-gray-900'),\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, this),\n                                            feature.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm truncate mt-0.5 \".concat(feature.isHighlighted ? 'text-purple-600' : feature.isIncluded ? 'text-green-600' : 'text-gray-600'),\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(feature.isIncluded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                            children: feature.isIncluded ? 'Included' : 'Not Included'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 flex items-center\",\n                                        children: feature.isHighlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                            children: \"Featured\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-40\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFeatureIncluded(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isIncluded ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                title: feature.isIncluded ? 'Mark as not included' : 'Mark as included',\n                                                children: feature.isIncluded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFeatureHighlighted(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isHighlighted ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                title: feature.isHighlighted ? 'Remove highlight' : 'Highlight feature',\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditFeature(feature),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Edit Feature\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteFeature(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                title: \"Delete Feature\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, this),\n            viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"transition-all duration-200 hover:bg-gray-50 \".concat(feature.isHighlighted ? 'bg-purple-50 border-l-4 border-l-purple-500' : feature.isIncluded ? 'bg-green-50 border-l-4 border-l-green-500' : 'bg-gray-50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas \".concat(getFeatureIcon(feature), \" text-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-bold text-gray-900 truncate leading-tight\",\n                                                            children: feature.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium \".concat(feature.isIncluded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                children: feature.isIncluded ? 'Included' : 'Not Included'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-1\",\n                                            children: feature.isHighlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-1 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureIncluded(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isIncluded ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isIncluded ? 'Mark as not included' : 'Mark as included',\n                                                        children: feature.isIncluded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureHighlighted(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isHighlighted ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isHighlighted ? 'Remove highlight' : 'Highlight feature',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleEditFeature(feature),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDeleteFeature(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 504,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 503,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingFeature ? 'Edit Feature' : 'Create Feature'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isIncluded,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isIncluded: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Included\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isHighlighted,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isHighlighted: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Highlighted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700\",\n                                                children: editingFeature ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 622,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 615,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 613,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionFeaturesManagement, \"+/CmXx/1GB1WO+DfqCKONQSdSds=\");\n_c = OptionFeaturesManagement;\nvar _c;\n$RefreshReg$(_c, \"OptionFeaturesManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/option-features-management.tsx\n"));

/***/ })

});