"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/service-management.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/services/service-management.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceManagement: () => (/* binding */ ServiceManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ServiceManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ServiceManagement(param) {\n    let { category, selectedService, onServiceSelect } = param;\n    _s();\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredServices, setFilteredServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingService, setEditingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    // Function to get service-specific icons based on service name/type\n    const getServiceIcon = (service)=>{\n        const name = service.name.toLowerCase();\n        // Web Development - More specific icons\n        if (name.includes('website') || name.includes('web development')) {\n            return 'fa-code text-blue-500';\n        }\n        if (name.includes('web design') || name.includes('frontend')) {\n            return 'fa-paint-brush text-purple-500';\n        }\n        if (name.includes('backend') || name.includes('api')) {\n            return 'fa-database text-gray-600';\n        }\n        // E-commerce\n        if (name.includes('ecommerce') || name.includes('online store') || name.includes('shop')) {\n            return 'fa-store text-orange-500';\n        }\n        if (name.includes('payment') || name.includes('checkout')) {\n            return 'fa-credit-card text-green-600';\n        }\n        // Mobile Development\n        if (name.includes('mobile app') || name.includes('ios') || name.includes('android')) {\n            return 'fa-mobile-alt text-green-500';\n        }\n        if (name.includes('react native') || name.includes('flutter')) {\n            return 'fa-mobile text-blue-400';\n        }\n        // Design Services\n        if (name.includes('logo') || name.includes('branding')) {\n            return 'fa-copyright text-pink-500';\n        }\n        if (name.includes('ui design') || name.includes('interface')) {\n            return 'fa-desktop text-purple-400';\n        }\n        if (name.includes('graphic design') || name.includes('print')) {\n            return 'fa-image text-red-400';\n        }\n        // Marketing & SEO\n        if (name.includes('seo') || name.includes('search engine')) {\n            return 'fa-search text-green-600';\n        }\n        if (name.includes('social media') || name.includes('facebook') || name.includes('instagram')) {\n            return 'fa-share-alt text-blue-600';\n        }\n        if (name.includes('email marketing') || name.includes('newsletter')) {\n            return 'fa-envelope text-red-500';\n        }\n        if (name.includes('advertising') || name.includes('ads')) {\n            return 'fa-bullhorn text-orange-600';\n        }\n        // Technical Services\n        if (name.includes('hosting') || name.includes('server')) {\n            return 'fa-server text-gray-500';\n        }\n        if (name.includes('domain') || name.includes('dns')) {\n            return 'fa-globe text-blue-300';\n        }\n        if (name.includes('ssl') || name.includes('certificate')) {\n            return 'fa-lock text-green-700';\n        }\n        if (name.includes('backup') || name.includes('restore')) {\n            return 'fa-cloud text-gray-400';\n        }\n        // Maintenance & Support\n        if (name.includes('maintenance') || name.includes('update')) {\n            return 'fa-wrench text-yellow-500';\n        }\n        if (name.includes('support') || name.includes('help')) {\n            return 'fa-headset text-teal-500';\n        }\n        if (name.includes('monitoring') || name.includes('uptime')) {\n            return 'fa-heartbeat text-red-600';\n        }\n        // Analytics & Reporting\n        if (name.includes('analytics') || name.includes('google analytics')) {\n            return 'fa-chart-line text-teal-600';\n        }\n        if (name.includes('report') || name.includes('dashboard')) {\n            return 'fa-chart-bar text-indigo-500';\n        }\n        // Consulting & Strategy\n        if (name.includes('consulting') || name.includes('consultation')) {\n            return 'fa-user-tie text-indigo-600';\n        }\n        if (name.includes('strategy') || name.includes('planning')) {\n            return 'fa-chess text-purple-600';\n        }\n        if (name.includes('audit') || name.includes('review')) {\n            return 'fa-clipboard-check text-orange-400';\n        }\n        // Content Services\n        if (name.includes('content') || name.includes('copywriting')) {\n            return 'fa-pen text-blue-700';\n        }\n        if (name.includes('blog') || name.includes('article')) {\n            return 'fa-newspaper text-gray-700';\n        }\n        // Security Services\n        if (name.includes('security') || name.includes('penetration')) {\n            return 'fa-shield-alt text-red-500';\n        }\n        if (name.includes('firewall') || name.includes('protection')) {\n            return 'fa-shield text-red-700';\n        }\n        // Default fallback\n        return 'fa-cog text-gray-500';\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        price: 0,\n        discountRate: 0,\n        manager: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"ServiceManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"ServiceManagement.useEffect.timer\"], 300);\n            return ({\n                \"ServiceManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"ServiceManagement.useEffect\"];\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            fetchServices();\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        category.id\n    ]);\n    // Fetch services for the selected category\n    const fetchServices = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/admin/services?categoryId=\".concat(category.id, \"&limit=100\"));\n            if (response.ok) {\n                const data = await response.json();\n                const servicesData = data.data || data.services || [];\n                setServices(servicesData);\n                setFilteredServices(servicesData);\n            } else {\n                console.error('Failed to fetch services:', response.status, response.statusText);\n                setServices([]);\n                setFilteredServices([]);\n            }\n        } catch (error) {\n            console.error('Error fetching services:', error);\n            setServices([]);\n            setFilteredServices([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleServiceSelect = (service)=>{\n        onServiceSelect(service);\n    };\n    const handleCreateService = ()=>{\n        setIsFormOpen(true);\n        setEditingService(null);\n        setFormData({\n            name: '',\n            description: '',\n            price: 0,\n            discountRate: 0,\n            manager: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleEditService = (service)=>{\n        setEditingService(service);\n        setFormData({\n            name: service.name,\n            description: service.description,\n            price: service.price,\n            discountRate: service.discountRate || 0,\n            manager: service.manager || '',\n            isActive: service.isActive,\n            displayOrder: service.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteService = async (serviceId)=>{\n        if (confirm('Are you sure you want to delete this service?')) {\n            try {\n                const response = await fetch(\"/api/admin/services/\".concat(serviceId), {\n                    method: 'DELETE'\n                });\n                if (response.ok) {\n                    await fetchServices(); // Refresh the services list\n                } else {\n                    const errorData = await response.json();\n                    alert(errorData.message || 'Failed to delete service');\n                }\n            } catch (error) {\n                console.error('Error deleting service:', error);\n                alert('An error occurred while deleting the service');\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            const serviceData = {\n                servicename: formData.name,\n                servicedesc: formData.description,\n                categoryid: category.id,\n                price: formData.price,\n                discountrate: formData.discountRate,\n                totaldiscount: formData.discountRate > 0 ? formData.price * formData.discountRate / 100 : 0,\n                manager: formData.manager,\n                isactive: formData.isActive,\n                displayorder: formData.displayOrder,\n                iconclass: ''\n            };\n            const url = editingService ? \"/api/admin/services/\".concat(editingService.id) : '/api/admin/services';\n            const method = editingService ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(serviceData)\n            });\n            if (response.ok) {\n                setIsFormOpen(false);\n                setEditingService(null);\n                await fetchServices(); // Refresh the services list\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to save service');\n            }\n        } catch (error) {\n            console.error('Error saving service:', error);\n            alert('An error occurred while saving the service');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n            lineNumber: 343,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Services\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage services under \",\n                                    category.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateService,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Service\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search services...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this),\n            viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-200 border border-gray-300 rounded-none px-4 py-2 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: filteredServices.map((service)=>{\n                            var _service__count;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex items-center px-3 rounded-none cursor-pointer transition-all duration-200 border border-gray-200 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'bg-green-50 border-green-300' : 'bg-white hover:bg-gray-50'),\n                                onClick: ()=>handleServiceSelect(service),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-full flex items-center justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas \".concat(getServiceIcon(service), \" text-3xl\")\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0 flex flex-col justify-center ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-base truncate \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'text-green-900' : 'text-gray-900'),\n                                                children: service.name\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm truncate mt-0.5 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'text-green-600' : 'text-gray-600'),\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-bold text-green-600\",\n                                                children: [\n                                                    \"$\",\n                                                    service.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            service.discountRate && service.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-red-600 font-medium\",\n                                                children: [\n                                                    service.discountRate,\n                                                    \"% off\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                            children: ((_service__count = service._count) === null || _service__count === void 0 ? void 0 : _service__count.serviceOptions) || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(service.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                            children: service.isActive ? 'Active' : 'Inactive'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-32\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleEditService(service);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Edit Service\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDeleteService(service.id);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                title: \"Delete Service\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, service.id, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 396,\n                columnNumber: 9\n            }, this),\n            viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredServices.map((service)=>{\n                                var _service__count;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'bg-green-50 border-l-4 border-l-green-500' : ''),\n                                    onClick: ()=>handleServiceSelect(service),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas \".concat(getServiceIcon(service), \" text-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-bold text-gray-900 truncate leading-tight\",\n                                                            children: service.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-green-600 leading-tight\",\n                                                children: [\n                                                    \"$\",\n                                                    service.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: ((_service__count = service._count) === null || _service__count === void 0 ? void 0 : _service__count.serviceOptions) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium \".concat(service.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                children: service.isActive ? 'Active' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleEditService(service);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDeleteService(service.id);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, service.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 508,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingService ? 'Edit Service' : 'Create Service'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                rows: 3,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        step: \"0.01\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Discount Rate (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.discountRate,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discountRate: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        step: \"0.1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.manager,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        manager: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.isActive,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        isActive: e.target.checked\n                                                    }),\n                                                className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700\",\n                                                children: editingService ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 601,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, this);\n}\n_s(ServiceManagement, \"lxsAPW/HYYUim+cgjcxsBi+tIuI=\");\n_c = ServiceManagement;\nvar _c;\n$RefreshReg$(_c, \"ServiceManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/service-management.tsx\n"));

/***/ })

});