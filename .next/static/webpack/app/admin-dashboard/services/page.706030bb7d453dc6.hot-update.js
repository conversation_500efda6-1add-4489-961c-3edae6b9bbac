"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/service-management.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/services/service-management.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceManagement: () => (/* binding */ ServiceManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ServiceManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ServiceManagement(param) {\n    let { category, selectedService, onServiceSelect } = param;\n    _s();\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredServices, setFilteredServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingService, setEditingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    // Function to get service-specific icons based on service name/type\n    const getServiceIcon = (service)=>{\n        const name = service.name.toLowerCase();\n        // Web Development - More specific icons\n        if (name.includes('website') || name.includes('web development')) {\n            return 'fa-code text-blue-500';\n        }\n        if (name.includes('web design') || name.includes('frontend')) {\n            return 'fa-paint-brush text-purple-500';\n        }\n        if (name.includes('backend') || name.includes('api')) {\n            return 'fa-database text-gray-600';\n        }\n        // E-commerce\n        if (name.includes('ecommerce') || name.includes('online store') || name.includes('shop')) {\n            return 'fa-store text-orange-500';\n        }\n        if (name.includes('payment') || name.includes('checkout')) {\n            return 'fa-credit-card text-green-600';\n        }\n        // Mobile Development\n        if (name.includes('mobile app') || name.includes('ios') || name.includes('android')) {\n            return 'fa-mobile-alt text-green-500';\n        }\n        if (name.includes('react native') || name.includes('flutter')) {\n            return 'fa-mobile text-blue-400';\n        }\n        // Design Services\n        if (name.includes('logo') || name.includes('branding')) {\n            return 'fa-copyright text-pink-500';\n        }\n        if (name.includes('ui design') || name.includes('interface')) {\n            return 'fa-desktop text-purple-400';\n        }\n        if (name.includes('graphic design') || name.includes('print')) {\n            return 'fa-image text-red-400';\n        }\n        // Marketing & SEO\n        if (name.includes('seo') || name.includes('search engine')) {\n            return 'fa-search text-green-600';\n        }\n        if (name.includes('social media') || name.includes('facebook') || name.includes('instagram')) {\n            return 'fa-share-alt text-blue-600';\n        }\n        if (name.includes('email marketing') || name.includes('newsletter')) {\n            return 'fa-envelope text-red-500';\n        }\n        if (name.includes('advertising') || name.includes('ads')) {\n            return 'fa-bullhorn text-orange-600';\n        }\n        // Technical Services\n        if (name.includes('hosting') || name.includes('server')) {\n            return 'fa-server text-gray-500';\n        }\n        if (name.includes('domain') || name.includes('dns')) {\n            return 'fa-globe text-blue-300';\n        }\n        if (name.includes('ssl') || name.includes('certificate')) {\n            return 'fa-lock text-green-700';\n        }\n        if (name.includes('backup') || name.includes('restore')) {\n            return 'fa-cloud text-gray-400';\n        }\n        // Maintenance & Support\n        if (name.includes('maintenance') || name.includes('update')) {\n            return 'fa-wrench text-yellow-500';\n        }\n        if (name.includes('support') || name.includes('help')) {\n            return 'fa-headset text-teal-500';\n        }\n        if (name.includes('monitoring') || name.includes('uptime')) {\n            return 'fa-heartbeat text-red-600';\n        }\n        // Analytics & Reporting\n        if (name.includes('analytics') || name.includes('google analytics')) {\n            return 'fa-chart-line text-teal-600';\n        }\n        if (name.includes('report') || name.includes('dashboard')) {\n            return 'fa-chart-bar text-indigo-500';\n        }\n        // Consulting & Strategy\n        if (name.includes('consulting') || name.includes('consultation')) {\n            return 'fa-user-tie text-indigo-600';\n        }\n        if (name.includes('strategy') || name.includes('planning')) {\n            return 'fa-chess text-purple-600';\n        }\n        if (name.includes('audit') || name.includes('review')) {\n            return 'fa-clipboard-check text-orange-400';\n        }\n        // Content Services\n        if (name.includes('content') || name.includes('copywriting')) {\n            return 'fa-pen text-blue-700';\n        }\n        if (name.includes('blog') || name.includes('article')) {\n            return 'fa-newspaper text-gray-700';\n        }\n        // Security Services\n        if (name.includes('security') || name.includes('penetration')) {\n            return 'fa-shield-alt text-red-500';\n        }\n        if (name.includes('firewall') || name.includes('protection')) {\n            return 'fa-shield text-red-700';\n        }\n        // Default fallback\n        return 'fa-cog text-gray-500';\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        price: 0,\n        discountRate: 0,\n        manager: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"ServiceManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"ServiceManagement.useEffect.timer\"], 300);\n            return ({\n                \"ServiceManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"ServiceManagement.useEffect\"];\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            fetchServices();\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        category.id\n    ]);\n    // Fetch services for the selected category\n    const fetchServices = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/admin/services?categoryId=\".concat(category.id, \"&limit=100\"));\n            if (response.ok) {\n                const data = await response.json();\n                const servicesData = data.data || data.services || [];\n                setServices(servicesData);\n                setFilteredServices(servicesData);\n            } else {\n                console.error('Failed to fetch services:', response.status, response.statusText);\n                setServices([]);\n                setFilteredServices([]);\n            }\n        } catch (error) {\n            console.error('Error fetching services:', error);\n            setServices([]);\n            setFilteredServices([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleServiceSelect = (service)=>{\n        onServiceSelect(service);\n    };\n    const handleCreateService = ()=>{\n        setIsFormOpen(true);\n        setEditingService(null);\n        setFormData({\n            name: '',\n            description: '',\n            price: 0,\n            discountRate: 0,\n            manager: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleEditService = (service)=>{\n        setEditingService(service);\n        setFormData({\n            name: service.name,\n            description: service.description,\n            price: service.price,\n            discountRate: service.discountRate || 0,\n            manager: service.manager || '',\n            isActive: service.isActive,\n            displayOrder: service.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteService = async (serviceId)=>{\n        if (confirm('Are you sure you want to delete this service?')) {\n            try {\n                const response = await fetch(\"/api/admin/services/\".concat(serviceId), {\n                    method: 'DELETE'\n                });\n                if (response.ok) {\n                    await fetchServices(); // Refresh the services list\n                } else {\n                    const errorData = await response.json();\n                    alert(errorData.message || 'Failed to delete service');\n                }\n            } catch (error) {\n                console.error('Error deleting service:', error);\n                alert('An error occurred while deleting the service');\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            const serviceData = {\n                servicename: formData.name,\n                servicedesc: formData.description,\n                categoryid: category.id,\n                price: formData.price,\n                discountrate: formData.discountRate,\n                totaldiscount: formData.discountRate > 0 ? formData.price * formData.discountRate / 100 : 0,\n                manager: formData.manager,\n                isactive: formData.isActive,\n                displayorder: formData.displayOrder,\n                iconclass: ''\n            };\n            const url = editingService ? \"/api/admin/services/\".concat(editingService.id) : '/api/admin/services';\n            const method = editingService ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(serviceData)\n            });\n            if (response.ok) {\n                setIsFormOpen(false);\n                setEditingService(null);\n                await fetchServices(); // Refresh the services list\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to save service');\n            }\n        } catch (error) {\n            console.error('Error saving service:', error);\n            alert('An error occurred while saving the service');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n            lineNumber: 343,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Services\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage services under \",\n                                    category.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateService,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Service\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search services...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this),\n            viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-200 border border-gray-300 rounded-none px-4 py-2 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: filteredServices.map((service)=>{\n                            var _service__count;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex items-center px-3 rounded-none cursor-pointer transition-all duration-200 border border-gray-200 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'bg-green-50 border-green-300' : 'bg-white hover:bg-gray-50'),\n                                onClick: ()=>handleServiceSelect(service),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-full flex items-center justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas \".concat(getServiceIcon(service), \" text-3xl\")\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0 flex flex-col justify-center ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-base truncate \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'text-green-900' : 'text-gray-900'),\n                                                children: service.name\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm truncate mt-0.5 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'text-green-600' : 'text-gray-600'),\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-bold text-green-600\",\n                                                children: [\n                                                    \"$\",\n                                                    service.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            service.discountRate && service.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-red-600 font-medium\",\n                                                children: [\n                                                    service.discountRate,\n                                                    \"% off\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                            children: ((_service__count = service._count) === null || _service__count === void 0 ? void 0 : _service__count.serviceOptions) || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(service.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                            children: service.isActive ? 'Active' : 'Inactive'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-32\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleEditService(service);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Edit Service\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDeleteService(service.id);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                title: \"Delete Service\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, service.id, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 396,\n                columnNumber: 9\n            }, this),\n            viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 rounded-none border border-gray-300 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-800\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-1 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-1 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredServices.map((service)=>{\n                                var _service__count;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'bg-green-50 border-l-4 border-l-green-500' : ''),\n                                    onClick: ()=>handleServiceSelect(service),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas \".concat(getServiceIcon(service), \" text-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-bold text-gray-900 truncate leading-tight\",\n                                                            children: service.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-green-600 leading-tight\",\n                                                children: [\n                                                    \"$\",\n                                                    service.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: ((_service__count = service._count) === null || _service__count === void 0 ? void 0 : _service__count.serviceOptions) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium \".concat(service.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                children: service.isActive ? 'Active' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleEditService(service);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDeleteService(service.id);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, service.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 508,\n                columnNumber: 21\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingService ? 'Edit Service' : 'Create Service'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                rows: 3,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        step: \"0.01\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Discount Rate (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.discountRate,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discountRate: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        step: \"0.1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.manager,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        manager: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.isActive,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        isActive: e.target.checked\n                                                    }),\n                                                className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700\",\n                                                children: editingService ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 601,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, this);\n}\n_s(ServiceManagement, \"lxsAPW/HYYUim+cgjcxsBi+tIuI=\");\n_c = ServiceManagement;\nvar _c;\n$RefreshReg$(_c, \"ServiceManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/service-management.tsx\n"));

/***/ })

});