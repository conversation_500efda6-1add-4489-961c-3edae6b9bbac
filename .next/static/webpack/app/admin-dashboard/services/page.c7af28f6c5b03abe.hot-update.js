"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/service-management.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/services/service-management.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceManagement: () => (/* binding */ ServiceManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _category_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./category-header */ \"(app-pages-browser)/./src/components/admin/services/category-header.tsx\");\n/* __next_internal_client_entry_do_not_use__ ServiceManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ServiceManagement(param) {\n    let { category, selectedService, onServiceSelect } = param;\n    _s();\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredServices, setFilteredServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingService, setEditingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    // Function to get service-specific icons based on service name/type\n    const getServiceIcon = (service)=>{\n        const name = service.name.toLowerCase();\n        // Web Development - More specific icons\n        if (name.includes('website') || name.includes('web development')) {\n            return 'fa-code text-blue-500';\n        }\n        if (name.includes('web design') || name.includes('frontend')) {\n            return 'fa-paint-brush text-purple-500';\n        }\n        if (name.includes('backend') || name.includes('api')) {\n            return 'fa-database text-gray-600';\n        }\n        // E-commerce\n        if (name.includes('ecommerce') || name.includes('online store') || name.includes('shop')) {\n            return 'fa-store text-orange-500';\n        }\n        if (name.includes('payment') || name.includes('checkout')) {\n            return 'fa-credit-card text-green-600';\n        }\n        // Mobile Development\n        if (name.includes('mobile app') || name.includes('ios') || name.includes('android')) {\n            return 'fa-mobile-alt text-green-500';\n        }\n        if (name.includes('react native') || name.includes('flutter')) {\n            return 'fa-mobile text-blue-400';\n        }\n        // Design Services\n        if (name.includes('logo') || name.includes('branding')) {\n            return 'fa-copyright text-pink-500';\n        }\n        if (name.includes('ui design') || name.includes('interface')) {\n            return 'fa-desktop text-purple-400';\n        }\n        if (name.includes('graphic design') || name.includes('print')) {\n            return 'fa-image text-red-400';\n        }\n        // Marketing & SEO\n        if (name.includes('seo') || name.includes('search engine')) {\n            return 'fa-search text-green-600';\n        }\n        if (name.includes('social media') || name.includes('facebook') || name.includes('instagram')) {\n            return 'fa-share-alt text-blue-600';\n        }\n        if (name.includes('email marketing') || name.includes('newsletter')) {\n            return 'fa-envelope text-red-500';\n        }\n        if (name.includes('advertising') || name.includes('ads')) {\n            return 'fa-bullhorn text-orange-600';\n        }\n        // Technical Services\n        if (name.includes('hosting') || name.includes('server')) {\n            return 'fa-server text-gray-500';\n        }\n        if (name.includes('domain') || name.includes('dns')) {\n            return 'fa-globe text-blue-300';\n        }\n        if (name.includes('ssl') || name.includes('certificate')) {\n            return 'fa-lock text-green-700';\n        }\n        if (name.includes('backup') || name.includes('restore')) {\n            return 'fa-cloud text-gray-400';\n        }\n        // Maintenance & Support\n        if (name.includes('maintenance') || name.includes('update')) {\n            return 'fa-wrench text-yellow-500';\n        }\n        if (name.includes('support') || name.includes('help')) {\n            return 'fa-headset text-teal-500';\n        }\n        if (name.includes('monitoring') || name.includes('uptime')) {\n            return 'fa-heartbeat text-red-600';\n        }\n        // Analytics & Reporting\n        if (name.includes('analytics') || name.includes('google analytics')) {\n            return 'fa-chart-line text-teal-600';\n        }\n        if (name.includes('report') || name.includes('dashboard')) {\n            return 'fa-chart-bar text-indigo-500';\n        }\n        // Consulting & Strategy\n        if (name.includes('consulting') || name.includes('consultation')) {\n            return 'fa-user-tie text-indigo-600';\n        }\n        if (name.includes('strategy') || name.includes('planning')) {\n            return 'fa-chess text-purple-600';\n        }\n        if (name.includes('audit') || name.includes('review')) {\n            return 'fa-clipboard-check text-orange-400';\n        }\n        // Content Services\n        if (name.includes('content') || name.includes('copywriting')) {\n            return 'fa-pen text-blue-700';\n        }\n        if (name.includes('blog') || name.includes('article')) {\n            return 'fa-newspaper text-gray-700';\n        }\n        // Security Services\n        if (name.includes('security') || name.includes('penetration')) {\n            return 'fa-shield-alt text-red-500';\n        }\n        if (name.includes('firewall') || name.includes('protection')) {\n            return 'fa-shield text-red-700';\n        }\n        // Default fallback\n        return 'fa-cog text-gray-500';\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        price: 0,\n        discountRate: 0,\n        manager: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"ServiceManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"ServiceManagement.useEffect.timer\"], 300);\n            return ({\n                \"ServiceManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"ServiceManagement.useEffect\"];\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            fetchServices();\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        category.id\n    ]);\n    // Fetch services for the selected category\n    const fetchServices = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/admin/services?categoryId=\".concat(category.id, \"&limit=100\"));\n            if (response.ok) {\n                const data = await response.json();\n                const servicesData = data.data || data.services || [];\n                setServices(servicesData);\n                setFilteredServices(servicesData);\n            } else {\n                console.error('Failed to fetch services:', response.status, response.statusText);\n                setServices([]);\n                setFilteredServices([]);\n            }\n        } catch (error) {\n            console.error('Error fetching services:', error);\n            setServices([]);\n            setFilteredServices([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleServiceSelect = (service)=>{\n        onServiceSelect(service);\n    };\n    const handleCreateService = ()=>{\n        setIsFormOpen(true);\n        setEditingService(null);\n        setFormData({\n            name: '',\n            description: '',\n            price: 0,\n            discountRate: 0,\n            manager: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleEditService = (service)=>{\n        setEditingService(service);\n        setFormData({\n            name: service.name,\n            description: service.description,\n            price: service.price,\n            discountRate: service.discountRate || 0,\n            manager: service.manager || '',\n            isActive: service.isActive,\n            displayOrder: service.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteService = async (serviceId)=>{\n        if (confirm('Are you sure you want to delete this service?')) {\n            try {\n                const response = await fetch(\"/api/admin/services/\".concat(serviceId), {\n                    method: 'DELETE'\n                });\n                if (response.ok) {\n                    await fetchServices(); // Refresh the services list\n                } else {\n                    const errorData = await response.json();\n                    alert(errorData.message || 'Failed to delete service');\n                }\n            } catch (error) {\n                console.error('Error deleting service:', error);\n                alert('An error occurred while deleting the service');\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            const serviceData = {\n                servicename: formData.name,\n                servicedesc: formData.description,\n                categoryid: category.id,\n                price: formData.price,\n                discountrate: formData.discountRate,\n                totaldiscount: formData.discountRate > 0 ? formData.price * formData.discountRate / 100 : 0,\n                manager: formData.manager,\n                isactive: formData.isActive,\n                displayorder: formData.displayOrder,\n                iconclass: ''\n            };\n            const url = editingService ? \"/api/admin/services/\".concat(editingService.id) : '/api/admin/services';\n            const method = editingService ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(serviceData)\n            });\n            if (response.ok) {\n                setIsFormOpen(false);\n                setEditingService(null);\n                await fetchServices(); // Refresh the services list\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to save service');\n            }\n        } catch (error) {\n            console.error('Error saving service:', error);\n            alert('An error occurred while saving the service');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 345,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n            lineNumber: 344,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_category_header__WEBPACK_IMPORTED_MODULE_2__.CategoryHeader, {\n                title: \"Services\",\n                description: \"Manage services under \".concat(category.name),\n                enableCreate: true,\n                createButtonText: \"Add Service\",\n                onCreateClick: handleCreateService,\n                viewMode: viewMode,\n                onViewModeChange: setViewMode,\n                searchTerm: searchTerm,\n                onSearchChange: setSearchTerm,\n                searchPlaceholder: \"Search services...\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search services...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this),\n            viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: filteredServices.map((service)=>{\n                            var _service__count;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex items-center px-3 rounded-none cursor-pointer transition-all duration-200 border border-gray-200 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'bg-green-50 border-green-300' : 'bg-white hover:bg-gray-50'),\n                                onClick: ()=>handleServiceSelect(service),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-full flex items-center justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas \".concat(getServiceIcon(service), \" text-3xl\")\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0 flex flex-col justify-center ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-base truncate \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'text-green-900' : 'text-gray-900'),\n                                                children: service.name\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm truncate mt-0.5 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'text-green-600' : 'text-gray-600'),\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-bold text-green-600\",\n                                                children: [\n                                                    \"$\",\n                                                    service.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            service.discountRate && service.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-red-600 font-medium\",\n                                                children: [\n                                                    service.discountRate,\n                                                    \"% off\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                            children: ((_service__count = service._count) === null || _service__count === void 0 ? void 0 : _service__count.serviceOptions) || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(service.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                            children: service.isActive ? 'Active' : 'Inactive'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-32\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleEditService(service);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Edit Service\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDeleteService(service.id);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                title: \"Delete Service\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, service.id, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 396,\n                columnNumber: 9\n            }, this),\n            viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredServices.map((service)=>{\n                                var _service__count;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'bg-green-50 border-l-4 border-l-green-500' : ''),\n                                    onClick: ()=>handleServiceSelect(service),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas \".concat(getServiceIcon(service), \" text-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-bold text-gray-900 truncate leading-tight\",\n                                                            children: service.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-green-600 leading-tight\",\n                                                children: [\n                                                    \"$\",\n                                                    service.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: ((_service__count = service._count) === null || _service__count === void 0 ? void 0 : _service__count.serviceOptions) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium \".concat(service.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                children: service.isActive ? 'Active' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-0.5 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleEditService(service);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDeleteService(service.id);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, service.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 508,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingService ? 'Edit Service' : 'Create Service'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                rows: 3,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        step: \"0.01\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Discount Rate (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.discountRate,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discountRate: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        step: \"0.1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.manager,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        manager: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.isActive,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        isActive: e.target.checked\n                                                    }),\n                                                className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700\",\n                                                children: editingService ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 601,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n        lineNumber: 358,\n        columnNumber: 5\n    }, this);\n}\n_s(ServiceManagement, \"lxsAPW/HYYUim+cgjcxsBi+tIuI=\");\n_c = ServiceManagement;\nvar _c;\n$RefreshReg$(_c, \"ServiceManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/service-management.tsx\n"));

/***/ })

});