"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/option-features-management.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/admin/services/option-features-management.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionFeaturesManagement: () => (/* binding */ OptionFeaturesManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PencilIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PencilIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PencilIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PencilIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PencilIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _category_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./category-header */ \"(app-pages-browser)/./src/components/admin/services/category-header.tsx\");\n/* __next_internal_client_entry_do_not_use__ OptionFeaturesManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction OptionFeaturesManagement(param) {\n    let { option } = param;\n    _s();\n    const [features, setFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredFeatures, setFilteredFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFeature, setEditingFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    // Function to get feature-specific icons based on feature name/type\n    const getFeatureIcon = (feature)=>{\n        const name = feature.name.toLowerCase();\n        // Design/UI features\n        if (name.includes('design') || name.includes('template') || name.includes('theme') || name.includes('layout')) {\n            return 'fa-palette text-purple-500';\n        }\n        // Content/Pages features\n        if (name.includes('page') || name.includes('content') || name.includes('blog') || name.includes('article')) {\n            return 'fa-file-alt text-blue-500';\n        }\n        // E-commerce features\n        if (name.includes('product') || name.includes('cart') || name.includes('payment') || name.includes('checkout')) {\n            return 'fa-shopping-cart text-orange-500';\n        }\n        // SEO/Marketing features\n        if (name.includes('seo') || name.includes('meta') || name.includes('analytics') || name.includes('tracking')) {\n            return 'fa-search text-green-500';\n        }\n        // Security features\n        if (name.includes('ssl') || name.includes('security') || name.includes('backup') || name.includes('protection')) {\n            return 'fa-shield-alt text-red-500';\n        }\n        // Performance features\n        if (name.includes('speed') || name.includes('cache') || name.includes('optimization') || name.includes('performance')) {\n            return 'fa-tachometer-alt text-yellow-500';\n        }\n        // Integration features\n        if (name.includes('integration') || name.includes('api') || name.includes('plugin') || name.includes('extension')) {\n            return 'fa-plug text-indigo-500';\n        }\n        // Support features\n        if (name.includes('support') || name.includes('help') || name.includes('documentation') || name.includes('training')) {\n            return 'fa-life-ring text-teal-500';\n        }\n        // Mobile features\n        if (name.includes('mobile') || name.includes('responsive') || name.includes('app')) {\n            return 'fa-mobile-alt text-pink-500';\n        }\n        // Social features\n        if (name.includes('social') || name.includes('share') || name.includes('facebook') || name.includes('twitter')) {\n            return 'fa-share-alt text-blue-400';\n        }\n        // Database/Storage features\n        if (name.includes('database') || name.includes('storage') || name.includes('hosting') || name.includes('server')) {\n            return 'fa-database text-gray-500';\n        }\n        // Default based on status\n        if (feature.isHighlighted) {\n            return 'fa-star text-yellow-400';\n        }\n        if (feature.isIncluded) {\n            return 'fa-check-circle text-green-500';\n        }\n        return 'fa-circle text-gray-400';\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        isIncluded: true,\n        isHighlighted: false,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"OptionFeaturesManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"OptionFeaturesManagement.useEffect.timer\"], 300);\n            return ({\n                \"OptionFeaturesManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"OptionFeaturesManagement.useEffect\"];\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            fetchFeatures();\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        option.id\n    ]);\n    // Mock data for demonstration\n    const fetchFeatures = async ()=>{\n        setLoading(true);\n        try {\n            // Mock data - replace with actual API call\n            const mockFeatures = [\n                {\n                    id: '1',\n                    optionId: option.id,\n                    name: 'Responsive Design',\n                    description: 'Mobile-friendly design that works on all devices',\n                    isIncluded: true,\n                    isHighlighted: true,\n                    displayOrder: 1,\n                    createdAt: '2024-01-15T10:00:00Z',\n                    updatedAt: '2024-01-15T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '2',\n                    optionId: option.id,\n                    name: 'SEO Optimization',\n                    description: 'Search engine optimization for better visibility',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 2,\n                    createdAt: '2024-01-14T10:00:00Z',\n                    updatedAt: '2024-01-14T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '3',\n                    optionId: option.id,\n                    name: 'Analytics Integration',\n                    description: 'Google Analytics and tracking setup',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 3,\n                    createdAt: '2024-01-13T10:00:00Z',\n                    updatedAt: '2024-01-13T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '4',\n                    optionId: option.id,\n                    name: 'Premium Support',\n                    description: '24/7 priority customer support',\n                    isIncluded: false,\n                    isHighlighted: true,\n                    displayOrder: 4,\n                    createdAt: '2024-01-12T10:00:00Z',\n                    updatedAt: '2024-01-12T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                }\n            ];\n            setFeatures(mockFeatures);\n            setFilteredFeatures(mockFeatures);\n        } catch (error) {\n            console.error('Error fetching features:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateFeature = ()=>{\n        setIsFormOpen(true);\n        setEditingFeature(null);\n        setFormData({\n            name: '',\n            description: '',\n            isIncluded: true,\n            isHighlighted: false,\n            displayOrder: 0\n        });\n    };\n    const handleEditFeature = (feature)=>{\n        setEditingFeature(feature);\n        setFormData({\n            name: feature.name,\n            description: feature.description || '',\n            isIncluded: feature.isIncluded,\n            isHighlighted: feature.isHighlighted,\n            displayOrder: feature.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteFeature = async (featureId)=>{\n        if (confirm('Are you sure you want to delete this feature?')) {\n            try {\n                // Mock delete - replace with actual API call\n                setFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n                setFilteredFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n            } catch (error) {\n                console.error('Error deleting feature:', error);\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            if (editingFeature) {\n                // Update existing feature\n                const updatedFeature = {\n                    ...editingFeature,\n                    ...formData\n                };\n                setFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n                setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n            } else {\n                // Create new feature\n                const newFeature = {\n                    id: Date.now().toString(),\n                    optionId: option.id,\n                    ...formData,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                };\n                setFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n                setFilteredFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n            }\n            setIsFormOpen(false);\n            setEditingFeature(null);\n        } catch (error) {\n            console.error('Error saving feature:', error);\n        }\n    };\n    const toggleFeatureIncluded = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature:', error);\n        }\n    };\n    const toggleFeatureHighlighted = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature highlight:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n            lineNumber: 323,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_category_header__WEBPACK_IMPORTED_MODULE_2__.CategoryHeader, {\n                title: \"Option Features\",\n                description: \"Manage features for \".concat(option.name),\n                enableCreate: true,\n                createButtonText: \"Add Feature\",\n                onCreateClick: handleCreateFeature,\n                viewMode: viewMode,\n                onViewModeChange: setViewMode,\n                searchTerm: searchTerm,\n                onSearchChange: setSearchTerm,\n                searchPlaceholder: \"Search features...\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-40\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: filteredFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex items-center px-3 rounded-none transition-all duration-200 border border-gray-200 \".concat(feature.isHighlighted ? 'bg-purple-50 border-purple-300' : feature.isIncluded ? 'bg-green-50 border-green-300' : 'bg-white hover:bg-gray-50'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-full flex items-center justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas \".concat(getFeatureIcon(feature), \" text-3xl\")\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0 flex flex-col justify-center ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-base truncate \".concat(feature.isHighlighted ? 'text-purple-900' : feature.isIncluded ? 'text-green-900' : 'text-gray-900'),\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            feature.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm truncate mt-0.5 \".concat(feature.isHighlighted ? 'text-purple-600' : feature.isIncluded ? 'text-green-600' : 'text-gray-600'),\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(feature.isIncluded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                            children: feature.isIncluded ? 'Included' : 'Not Included'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 flex items-center\",\n                                        children: feature.isHighlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                            children: \"Featured\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-40\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFeatureIncluded(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isIncluded ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                title: feature.isIncluded ? 'Mark as not included' : 'Mark as included',\n                                                children: feature.isIncluded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFeatureHighlighted(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isHighlighted ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                title: feature.isHighlighted ? 'Remove highlight' : 'Highlight feature',\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditFeature(feature),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Edit Feature\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteFeature(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                title: \"Delete Feature\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this),\n            viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-3 py-2 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"transition-all duration-200 hover:bg-gray-50 \".concat(feature.isHighlighted ? 'bg-purple-50 border-l-4 border-l-purple-500' : feature.isIncluded ? 'bg-green-50 border-l-4 border-l-green-500' : 'bg-gray-50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas \".concat(getFeatureIcon(feature), \" text-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-bold text-gray-900 truncate leading-tight\",\n                                                            children: feature.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium \".concat(feature.isIncluded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                children: feature.isIncluded ? 'Included' : 'Not Included'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-1\",\n                                            children: feature.isHighlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-1 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureIncluded(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isIncluded ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isIncluded ? 'Mark as not included' : 'Mark as included',\n                                                        children: feature.isIncluded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureHighlighted(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isHighlighted ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isHighlighted ? 'Remove highlight' : 'Highlight feature',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleEditFeature(feature),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDeleteFeature(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PencilIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 482,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingFeature ? 'Edit Feature' : 'Create Feature'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isIncluded,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isIncluded: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Included\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isHighlighted,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isHighlighted: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Highlighted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700\",\n                                                children: editingFeature ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 601,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionFeaturesManagement, \"+/CmXx/1GB1WO+DfqCKONQSdSds=\");\n_c = OptionFeaturesManagement;\nvar _c;\n$RefreshReg$(_c, \"OptionFeaturesManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/option-features-management.tsx\n"));

/***/ })

});