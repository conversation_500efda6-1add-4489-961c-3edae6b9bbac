'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { CategoryHeader } from './category-header'

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

interface OptionFeature {
  id: string
  optionId: string
  name: string
  description?: string
  isIncluded: boolean
  isHighlighted: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  option?: {
    id: string
    name: string
  }
}

interface OptionFeaturesManagementProps {
  option: ServiceOption
}

interface FeatureFormData {
  name: string
  description: string
  isIncluded: boolean
  isHighlighted: boolean
  displayOrder: number
}

export function OptionFeaturesManagement({ option }: OptionFeaturesManagementProps) {
  const [features, setFeatures] = useState<OptionFeature[]>([])
  const [filteredFeatures, setFilteredFeatures] = useState<OptionFeature[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingFeature, setEditingFeature] = useState<OptionFeature | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'table' | 'list'>('list')

  // Function to get feature-specific icons based on feature name/type
  const getFeatureIcon = (feature: OptionFeature): string => {
    const name = feature.name.toLowerCase()

    // Design/UI features
    if (name.includes('design') || name.includes('template') || name.includes('theme') || name.includes('layout')) {
      return 'fa-palette text-purple-500'
    }
    // Content/Pages features
    if (name.includes('page') || name.includes('content') || name.includes('blog') || name.includes('article')) {
      return 'fa-file-alt text-blue-500'
    }
    // E-commerce features
    if (name.includes('product') || name.includes('cart') || name.includes('payment') || name.includes('checkout')) {
      return 'fa-shopping-cart text-orange-500'
    }
    // SEO/Marketing features
    if (name.includes('seo') || name.includes('meta') || name.includes('analytics') || name.includes('tracking')) {
      return 'fa-search text-green-500'
    }
    // Security features
    if (name.includes('ssl') || name.includes('security') || name.includes('backup') || name.includes('protection')) {
      return 'fa-shield-alt text-red-500'
    }
    // Performance features
    if (name.includes('speed') || name.includes('cache') || name.includes('optimization') || name.includes('performance')) {
      return 'fa-tachometer-alt text-yellow-500'
    }
    // Integration features
    if (name.includes('integration') || name.includes('api') || name.includes('plugin') || name.includes('extension')) {
      return 'fa-plug text-indigo-500'
    }
    // Support features
    if (name.includes('support') || name.includes('help') || name.includes('documentation') || name.includes('training')) {
      return 'fa-life-ring text-teal-500'
    }
    // Mobile features
    if (name.includes('mobile') || name.includes('responsive') || name.includes('app')) {
      return 'fa-mobile-alt text-pink-500'
    }
    // Social features
    if (name.includes('social') || name.includes('share') || name.includes('facebook') || name.includes('twitter')) {
      return 'fa-share-alt text-blue-400'
    }
    // Database/Storage features
    if (name.includes('database') || name.includes('storage') || name.includes('hosting') || name.includes('server')) {
      return 'fa-database text-gray-500'
    }
    // Default based on status
    if (feature.isHighlighted) {
      return 'fa-star text-yellow-400'
    }
    if (feature.isIncluded) {
      return 'fa-check-circle text-green-500'
    }
    return 'fa-circle text-gray-400'
  }

  const [formData, setFormData] = useState<FeatureFormData>({
    name: '',
    description: '',
    isIncluded: true,
    isHighlighted: false,
    displayOrder: 0
  })

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    fetchFeatures()
  }, [option.id])

  // Mock data for demonstration
  const fetchFeatures = async () => {
    setLoading(true)
    try {
      // Mock data - replace with actual API call
      const mockFeatures: OptionFeature[] = [
        {
          id: '1',
          optionId: option.id,
          name: 'Responsive Design',
          description: 'Mobile-friendly design that works on all devices',
          isIncluded: true,
          isHighlighted: true,
          displayOrder: 1,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z',
          option: { id: option.id, name: option.name }
        },
        {
          id: '2',
          optionId: option.id,
          name: 'SEO Optimization',
          description: 'Search engine optimization for better visibility',
          isIncluded: true,
          isHighlighted: false,
          displayOrder: 2,
          createdAt: '2024-01-14T10:00:00Z',
          updatedAt: '2024-01-14T10:00:00Z',
          option: { id: option.id, name: option.name }
        },
        {
          id: '3',
          optionId: option.id,
          name: 'Analytics Integration',
          description: 'Google Analytics and tracking setup',
          isIncluded: true,
          isHighlighted: false,
          displayOrder: 3,
          createdAt: '2024-01-13T10:00:00Z',
          updatedAt: '2024-01-13T10:00:00Z',
          option: { id: option.id, name: option.name }
        },
        {
          id: '4',
          optionId: option.id,
          name: 'Premium Support',
          description: '24/7 priority customer support',
          isIncluded: false,
          isHighlighted: true,
          displayOrder: 4,
          createdAt: '2024-01-12T10:00:00Z',
          updatedAt: '2024-01-12T10:00:00Z',
          option: { id: option.id, name: option.name }
        }
      ]
      
      setFeatures(mockFeatures)
      setFilteredFeatures(mockFeatures)
    } catch (error) {
      console.error('Error fetching features:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateFeature = () => {
    setIsFormOpen(true)
    setEditingFeature(null)
    setFormData({
      name: '',
      description: '',
      isIncluded: true,
      isHighlighted: false,
      displayOrder: 0
    })
  }

  const handleEditFeature = (feature: OptionFeature) => {
    setEditingFeature(feature)
    setFormData({
      name: feature.name,
      description: feature.description || '',
      isIncluded: feature.isIncluded,
      isHighlighted: feature.isHighlighted,
      displayOrder: feature.displayOrder
    })
    setIsFormOpen(true)
  }

  const handleDeleteFeature = async (featureId: string) => {
    if (confirm('Are you sure you want to delete this feature?')) {
      try {
        // Mock delete - replace with actual API call
        setFeatures(prev => prev.filter(feature => feature.id !== featureId))
        setFilteredFeatures(prev => prev.filter(feature => feature.id !== featureId))
      } catch (error) {
        console.error('Error deleting feature:', error)
      }
    }
  }

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (editingFeature) {
        // Update existing feature
        const updatedFeature = { ...editingFeature, ...formData }
        setFeatures(prev => prev.map(feature => feature.id === editingFeature.id ? updatedFeature : feature))
        setFilteredFeatures(prev => prev.map(feature => feature.id === editingFeature.id ? updatedFeature : feature))
      } else {
        // Create new feature
        const newFeature: OptionFeature = {
          id: Date.now().toString(),
          optionId: option.id,
          ...formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          option: { id: option.id, name: option.name }
        }
        setFeatures(prev => [...prev, newFeature])
        setFilteredFeatures(prev => [...prev, newFeature])
      }
      setIsFormOpen(false)
      setEditingFeature(null)
    } catch (error) {
      console.error('Error saving feature:', error)
    }
  }

  const toggleFeatureIncluded = async (featureId: string) => {
    try {
      setFeatures(prev => prev.map(feature => 
        feature.id === featureId 
          ? { ...feature, isIncluded: !feature.isIncluded }
          : feature
      ))
      setFilteredFeatures(prev => prev.map(feature => 
        feature.id === featureId 
          ? { ...feature, isIncluded: !feature.isIncluded }
          : feature
      ))
    } catch (error) {
      console.error('Error toggling feature:', error)
    }
  }

  const toggleFeatureHighlighted = async (featureId: string) => {
    try {
      setFeatures(prev => prev.map(feature => 
        feature.id === featureId 
          ? { ...feature, isHighlighted: !feature.isHighlighted }
          : feature
      ))
      setFilteredFeatures(prev => prev.map(feature => 
        feature.id === featureId 
          ? { ...feature, isHighlighted: !feature.isHighlighted }
          : feature
      ))
    } catch (error) {
      console.error('Error toggling feature highlight:', error)
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <CategoryHeader
        title="Option Features"
        description={`Manage features for ${option.name}`}
        enableCreate={true}
        createButtonText="Add Feature"
        onCreateClick={handleCreateFeature}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="Search features..."
      />

      {/* Features Content */}
      {viewMode === 'list' && (
        <div>
          {/* List Headers */}
          <div className="bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2">
            <div className="flex items-center">
              <div className="w-6"></div> {/* Space for icon */}
              <div className="flex-1 min-w-0">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Feature</span>
              </div>
              <div className="w-24">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Status</span>
              </div>
              <div className="w-20">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Type</span>
              </div>
              <div className="w-40">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Actions</span>
              </div>
            </div>
          </div>

          {/* List Items */}
          <div className="space-y-1">
            {filteredFeatures.map((feature) => (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`flex items-center px-3 rounded-none transition-all duration-200 border border-gray-200 ${
                  feature.isHighlighted
                    ? 'bg-purple-50 border-purple-300'
                    : feature.isIncluded
                    ? 'bg-green-50 border-green-300'
                    : 'bg-white hover:bg-gray-50'
                }`}
              >
                {/* Feature Icon */}
                <div className="w-8 h-full flex items-center justify-start">
                  <i className={`fas ${getFeatureIcon(feature)} text-3xl`}></i>
                </div>

                {/* Feature Name & Description */}
                <div className="flex-1 min-w-0 flex flex-col justify-center ml-3">
                  <h3 className={`font-bold text-base truncate ${
                    feature.isHighlighted ? 'text-purple-900' :
                    feature.isIncluded ? 'text-green-900' : 'text-gray-900'
                  }`}>
                    {feature.name}
                  </h3>
                  {feature.description && (
                    <p className={`text-sm truncate mt-0.5 ${
                      feature.isHighlighted ? 'text-purple-600' :
                      feature.isIncluded ? 'text-green-600' : 'text-gray-600'
                    }`}>
                      {feature.description}
                    </p>
                  )}
                </div>

                {/* Status */}
                <div className="w-24 flex items-center">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium ${
                    feature.isIncluded
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {feature.isIncluded ? 'Included' : 'Not Included'}
                  </span>
                </div>

                {/* Type */}
                <div className="w-20 flex items-center">
                  {feature.isHighlighted && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium bg-purple-100 text-purple-800">
                      Featured
                    </span>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 w-40">
                  <button
                    onClick={() => toggleFeatureIncluded(feature.id)}
                    className={`inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white ${
                      feature.isIncluded
                        ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                        : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                    } focus:outline-none focus:ring-2 focus:ring-offset-2`}
                    title={feature.isIncluded ? 'Mark as not included' : 'Mark as included'}
                  >
                    {feature.isIncluded ? (
                      <XMarkIcon className="h-3 w-3" />
                    ) : (
                      <CheckIcon className="h-3 w-3" />
                    )}
                  </button>
                  <button
                    onClick={() => toggleFeatureHighlighted(feature.id)}
                    className={`inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white ${
                      feature.isHighlighted
                        ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500'
                        : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500'
                    } focus:outline-none focus:ring-2 focus:ring-offset-2`}
                    title={feature.isHighlighted ? 'Remove highlight' : 'Highlight feature'}
                  >
                    <StarIcon className="h-3 w-3" />
                  </button>
                  <button
                    onClick={() => handleEditFeature(feature)}
                    className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    title="Edit Feature"
                  >
                    <PencilIcon className="h-3 w-3" />
                  </button>
                  <button
                    onClick={() => handleDeleteFeature(feature.id)}
                    className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    title="Delete Feature"
                  >
                    <TrashIcon className="h-3 w-3" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {viewMode === 'table' && (
        <div className="bg-white rounded-none border border-gray-200 overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Feature
              </th>
              <th className="px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Status
              </th>
              <th className="px-3 py-2 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Type
              </th>
              <th className="px-3 py-2 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredFeatures.map((feature) => (
              <motion.tr
                key={feature.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`transition-all duration-200 hover:bg-gray-50 ${
                  feature.isHighlighted
                    ? 'bg-purple-50 border-l-4 border-l-purple-500'
                    : feature.isIncluded
                    ? 'bg-green-50 border-l-4 border-l-green-500'
                    : 'bg-gray-50'
                }`}
              >
                <td className="px-3 py-1">
                  <div className="flex items-center space-x-2">
                    <div className="flex-shrink-0">
                      <i className={`fas ${getFeatureIcon(feature)} text-sm`}></i>
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-bold text-gray-900 truncate leading-tight">{feature.name}</p>
                    </div>
                  </div>
                </td>
                <td className="px-3 py-1">
                  <span className={`inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium ${
                    feature.isIncluded
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {feature.isIncluded ? 'Included' : 'Not Included'}
                  </span>
                </td>
                <td className="px-3 py-1">
                  {feature.isHighlighted && (
                    <span className="inline-flex items-center px-1 py-0.5 rounded-none text-xs font-medium bg-purple-100 text-purple-800">
                      Featured
                    </span>
                  )}
                </td>
                <td className="px-3 py-1 text-right">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => toggleFeatureIncluded(feature.id)}
                      className={`inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white ${
                        feature.isIncluded
                          ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                          : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                      } focus:outline-none focus:ring-2 focus:ring-offset-2`}
                      title={feature.isIncluded ? 'Mark as not included' : 'Mark as included'}
                    >
                      {feature.isIncluded ? (
                        <XMarkIcon className="h-3 w-3" />
                      ) : (
                        <CheckIcon className="h-3 w-3" />
                      )}
                    </button>
                    <button
                      onClick={() => toggleFeatureHighlighted(feature.id)}
                      className={`inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white ${
                        feature.isHighlighted
                          ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500'
                          : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500'
                      } focus:outline-none focus:ring-2 focus:ring-offset-2`}
                      title={feature.isHighlighted ? 'Remove highlight' : 'Highlight feature'}
                    >
                      <StarIcon className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => handleEditFeature(feature)}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      title="Edit Feature"
                    >
                      <PencilIcon className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => handleDeleteFeature(feature.id)}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      title="Delete Feature"
                    >
                      <TrashIcon className="h-3 w-3" />
                    </button>
                  </div>
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
        </div>
      )}

      {/* Create/Edit Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-none p-6 w-full max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingFeature ? 'Edit Feature' : 'Create Feature'}
              </h3>
              <form onSubmit={handleSubmitForm} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                    rows={3}
                  />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.isIncluded}
                      onChange={(e) => setFormData({ ...formData, isIncluded: e.target.checked })}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900">Included</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.isHighlighted}
                      onChange={(e) => setFormData({ ...formData, isHighlighted: e.target.checked })}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900">Highlighted</label>
                  </div>
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setIsFormOpen(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700"
                  >
                    {editingFeature ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
