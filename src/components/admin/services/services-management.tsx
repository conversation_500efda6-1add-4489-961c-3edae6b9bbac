'use client'

import React, { useState, useCallback, memo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  FolderIcon,
  CogIcon,
  ListBulletIcon,
  StarIcon,
  ChevronRightIcon,
  PlusIcon,
  BuildingOfficeIcon,
  Cog6ToothIcon,
  RectangleStackIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import { CategoryManagement } from './category-management'
import { ServiceManagement } from './service-management'
import { ServiceOptionsManagement } from './service-options-management'
import { OptionFeaturesManagement } from './option-features-management'

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

type ActiveSection = 'categories' | 'services' | 'options' | 'features'

// Helper functions for theme-specific styling
const getActiveCardStyle = (sectionId: ActiveSection): string => {
  switch (sectionId) {
    case 'categories':
      return 'border-blue-100 shadow-sm bg-blue-25'
    case 'services':
      return 'border-emerald-100 shadow-sm bg-emerald-25'
    case 'options':
      return 'border-amber-100 shadow-sm bg-amber-25'
    case 'features':
      return 'border-purple-100 shadow-sm bg-purple-25'
    default:
      return 'border-gray-100 shadow-sm bg-gray-25'
  }
}

const getActiveIconColor = (sectionId: ActiveSection): string => {
  switch (sectionId) {
    case 'categories':
      return 'text-blue-600'
    case 'services':
      return 'text-emerald-600'
    case 'options':
      return 'text-amber-600'
    case 'features':
      return 'text-purple-600'
    default:
      return 'text-gray-600'
  }
}

const getHoverIconColor = (sectionId: ActiveSection): string => {
  switch (sectionId) {
    case 'categories':
      return 'text-blue-500'
    case 'services':
      return 'text-emerald-500'
    case 'options':
      return 'text-amber-500'
    case 'features':
      return 'text-purple-500'
    default:
      return 'text-gray-500'
  }
}

const getActiveTitleColor = (sectionId: ActiveSection): string => {
  switch (sectionId) {
    case 'categories':
      return 'text-blue-800'
    case 'services':
      return 'text-emerald-800'
    case 'options':
      return 'text-amber-800'
    case 'features':
      return 'text-purple-800'
    default:
      return 'text-gray-800'
  }
}

const getActiveDescriptionColor = (sectionId: ActiveSection): string => {
  switch (sectionId) {
    case 'categories':
      return 'text-blue-700'
    case 'services':
      return 'text-emerald-700'
    case 'options':
      return 'text-amber-700'
    case 'features':
      return 'text-purple-700'
    default:
      return 'text-gray-700'
  }
}

// Memoized section navigation component
const SectionNavigation = memo<{
  sections: readonly {
    readonly id: ActiveSection
    readonly title: string
    readonly description: string
    readonly color: string
    readonly gradient: string
    readonly icon: React.ComponentType<{ className?: string }>
    readonly isActive: boolean
    readonly disabled?: boolean
  }[]
  onSectionChange: (sectionId: ActiveSection) => void
}>(({ sections, onSectionChange }) => (
  <div className="overflow-visible">
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 min-w-[640px] md:min-w-0">
      {sections.map((section) => (
        <motion.button
          key={section.id}
          onClick={() => onSectionChange(section.id)}
          disabled={section.disabled}
          className={`group relative overflow-visible rounded-lg border transition-all duration-300 text-left ${
            section.isActive
              ? getActiveCardStyle(section.id)
              : section.disabled
              ? 'border-gray-200 cursor-not-allowed opacity-60 bg-gray-50'
              : 'border-gray-200 hover:border-gray-300 hover:shadow-lg hover:transform hover:scale-102 bg-white hover:bg-gray-50'
          }`}
          whileHover={!section.disabled ? { y: -2 } : undefined}
          whileTap={!section.disabled ? { scale: 0.98 } : undefined}
          aria-label={`Navigate to ${section.title} section`}
          aria-describedby={`${section.id}-description`}
          aria-current={section.isActive ? 'page' : undefined}
        >
          {/* Content */}
          <div className="relative p-3 rounded-lg">
            {/* Icon and Name on same row */}
            <div className="flex items-center space-x-3">
              <section.icon className={`h-8 w-8 transition-all duration-300 ${
                section.isActive
                  ? getActiveIconColor(section.id)
                  : section.disabled
                  ? 'text-gray-400'
                  : 'text-gray-600 group-hover:' + getHoverIconColor(section.id)
              }`} aria-hidden="true" />

              <h3 className={`text-lg font-semibold transition-colors duration-300 ${
                section.isActive
                  ? getActiveTitleColor(section.id)
                  : section.disabled
                  ? 'text-gray-400'
                  : 'text-gray-900'
              }`}>
                {section.title}
              </h3>
            </div>

            {/* Description below */}
            <div className="mt-1 ml-11">
              <p
                id={`${section.id}-description`}
                className={`text-sm leading-relaxed transition-colors duration-300 ${
                  section.isActive
                    ? getActiveDescriptionColor(section.id)
                    : section.disabled
                    ? 'text-gray-400'
                    : 'text-gray-600'
                }`}
              >
                {section.description}
              </p>
            </div>
          </div>
        </motion.button>
      ))}
    </div>
  </div>
))
SectionNavigation.displayName = 'SectionNavigation'

// Memoized breadcrumb component
const Breadcrumb = memo<{
  selectedCategory: Category | null
  selectedService: Service | null
  selectedOption: ServiceOption | null
}>(({ selectedCategory, selectedService, selectedOption }) => (
  <div className="mt-3 p-2 bg-gradient-to-r from-gray-50 to-gray-100/50 rounded-md border border-gray-200/50" aria-label="Navigation breadcrumb">
    <div className="flex items-center space-x-1">
      <span className="text-xs font-semibold text-gray-400 uppercase tracking-wide">Path:</span>
      <nav className="flex items-center space-x-1" aria-label="Breadcrumb">
        <div className="flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs font-medium">
          <BuildingOfficeIcon className="h-3 w-3" />
          <span>Categories</span>
        </div>
        {selectedCategory && selectedCategory.name && (
          <>
            <ChevronRightIcon className="h-3 w-3 text-gray-400" aria-hidden="true" />
            <div className="flex items-center space-x-1 px-2 py-1 bg-emerald-100 text-emerald-700 rounded text-xs font-medium">
              <FolderIcon className="h-3 w-3" />
              <span className="truncate max-w-24">{selectedCategory.name}</span>
            </div>
          </>
        )}
        {selectedService && selectedService.name && (
          <>
            <ChevronRightIcon className="h-3 w-3 text-gray-400" aria-hidden="true" />
            <div className="flex items-center space-x-1 px-2 py-1 bg-amber-100 text-amber-700 rounded text-xs font-medium">
              <Cog6ToothIcon className="h-3 w-3" />
              <span className="truncate max-w-24">{selectedService.name}</span>
            </div>
          </>
        )}
        {selectedOption && selectedOption.name && (
          <>
            <ChevronRightIcon className="h-3 w-3 text-gray-400" aria-hidden="true" />
            <div className="flex items-center space-x-1 px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs font-medium">
              <RectangleStackIcon className="h-3 w-3" />
              <span className="truncate max-w-24">{selectedOption.name}</span>
            </div>
          </>
        )}
      </nav>
    </div>
  </div>
))
Breadcrumb.displayName = 'Breadcrumb'

// Memoized header component
const Header = memo<{
  selectedCategory: Category | null
  selectedService: Service | null
  selectedOption: ServiceOption | null
}>(({ selectedCategory, selectedService, selectedOption }) => (
  <div className="relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden">
    {/* Background Pattern */}
    <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20" />

    <div className="relative p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-sm">
            <Cog6ToothIcon className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-gray-900">
              Services Management
            </h1>
            <p className="text-sm font-medium text-gray-600">
              Manage your service hierarchy
            </p>
          </div>
        </div>

        <div className="hidden lg:flex items-center space-x-2">
          <div className="h-2 w-2 bg-green-500 rounded-full" />
          <span className="text-xs font-semibold text-gray-700 uppercase tracking-wide">Active</span>
        </div>
      </div>

      <Breadcrumb
        selectedCategory={selectedCategory}
        selectedService={selectedService}
        selectedOption={selectedOption}
      />
    </div>
  </div>
))
Header.displayName = 'Header'

export function ServicesManagement() {
  const [activeSection, setActiveSection] = useState<ActiveSection>('categories')
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [selectedOption, setSelectedOption] = useState<ServiceOption | null>(null)

  const sections = [
    {
      id: 'categories' as const,
      title: 'Categories',
      description: 'Organize and structure your service categories with hierarchical management',
      color: 'bg-blue-500',
      gradient: 'bg-gradient-to-r from-blue-600 to-blue-700',
      icon: BuildingOfficeIcon,
      isActive: activeSection === 'categories',
      disabled: false
    },
    {
      id: 'services' as const,
      title: 'Services',
      description: 'Define and configure individual services within your categories',
      color: 'bg-emerald-500',
      gradient: 'bg-gradient-to-r from-emerald-600 to-emerald-700',
      icon: Cog6ToothIcon,
      isActive: activeSection === 'services',
      disabled: !selectedCategory
    },
    {
      id: 'options' as const,
      title: 'Service Options',
      description: 'Create customizable options and variations for your services',
      color: 'bg-amber-500',
      gradient: 'bg-gradient-to-r from-amber-600 to-amber-700',
      icon: RectangleStackIcon,
      isActive: activeSection === 'options',
      disabled: !selectedService
    },
    {
      id: 'features' as const,
      title: 'Option Features',
      description: 'Add detailed features and specifications to service options',
      color: 'bg-purple-500',
      gradient: 'bg-gradient-to-r from-purple-600 to-purple-700',
      icon: SparklesIcon,
      isActive: activeSection === 'features',
      disabled: !selectedOption
    }
  ] as const

  const handleSectionChange = useCallback((sectionId: ActiveSection) => {
    if (sections.find(s => s.id === sectionId)?.disabled) return
    setActiveSection(sectionId)
  }, [sections])

  const handleCategorySelect = useCallback((category: Category | null) => {
    setSelectedCategory(category)
    setSelectedService(null)
    setSelectedOption(null)
    if (category && activeSection === 'categories') {
      setActiveSection('services')
    }
  }, [activeSection])

  const handleServiceSelect = useCallback((service: Service | null) => {
    setSelectedService(service)
    setSelectedOption(null)
    if (service && activeSection === 'services') {
      setActiveSection('options')
    }
  }, [activeSection])

  const handleOptionSelect = useCallback((option: ServiceOption | null) => {
    setSelectedOption(option)
    if (option && activeSection === 'options') {
      setActiveSection('features')
    }
  }, [activeSection])

  return (
    <div className="h-full flex flex-col space-y-4">
        <Header
          selectedCategory={selectedCategory}
          selectedService={selectedService}
          selectedOption={selectedOption}
        />

        <SectionNavigation
          sections={sections}
          onSectionChange={handleSectionChange}
        />

        {/* Content Area */}
        <div
          className="flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden"
          role="main"
          aria-label={`${sections.find(s => s.isActive)?.title} management section`}
        >
          <AnimatePresence mode="wait">
            {activeSection === 'categories' && (
              <motion.div
                key="categories"
                data-section="categories"
                initial={{ opacity: 0, y: 30, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -30, scale: 0.95 }}
                transition={{
                  duration: 0.4,
                  ease: [0.4, 0.0, 0.2, 1],
                  scale: { duration: 0.3 }
                }}
              >
                <CategoryManagement
                  selectedCategory={selectedCategory}
                  onCategorySelect={handleCategorySelect}
                />
              </motion.div>
            )}

            {activeSection === 'services' && selectedCategory && (
              <motion.div
                key="services"
                data-section="services"
                initial={{ opacity: 0, y: 30, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -30, scale: 0.95 }}
                transition={{
                  duration: 0.4,
                  ease: [0.4, 0.0, 0.2, 1],
                  scale: { duration: 0.3 }
                }}
              >
                <ServiceManagement
                  category={selectedCategory}
                  selectedService={selectedService}
                  onServiceSelect={handleServiceSelect}
                />
              </motion.div>
            )}

            {activeSection === 'options' && selectedService && (
              <motion.div
                key="options"
                data-section="options"
                initial={{ opacity: 0, y: 30, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -30, scale: 0.95 }}
                transition={{
                  duration: 0.4,
                  ease: [0.4, 0.0, 0.2, 1],
                  scale: { duration: 0.3 }
                }}
              >
                <ServiceOptionsManagement
                  service={selectedService}
                  selectedOption={selectedOption}
                  onOptionSelect={handleOptionSelect}
                />
              </motion.div>
            )}

            {activeSection === 'features' && selectedOption && (
              <motion.div
                key="features"
                data-section="features"
                initial={{ opacity: 0, y: 30, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -30, scale: 0.95 }}
                transition={{
                  duration: 0.4,
                  ease: [0.4, 0.0, 0.2, 1],
                  scale: { duration: 0.3 }
                }}
              >
                <OptionFeaturesManagement
                  option={selectedOption}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
    </div>
  )
}
