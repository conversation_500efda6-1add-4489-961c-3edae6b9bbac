/* Admin Pages Global Styles */
/* This file provides complete styling for admin interfaces without any theme dependencies */
/* IMPORTANT: Admin pages should have NO theme classes applied at all */

/* ===== COMPLETE THEME OVERRIDE ===== */
/* Force remove any theme classes that might accidentally be applied */
html.theme-light .admin-page,
html.theme-dark .admin-page,
html.light .admin-page,
html.dark .admin-page,
.theme-light .admin-page,
.theme-dark .admin-page,
.light .admin-page,
.dark .admin-page,
.admin-page {
  /* Force original light mode styling */
  background-color: #f9fafb !important;
  color: #111827 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  line-height: 1.5 !important;
  min-height: 100vh !important;
  width: 100% !important;
}

/* ===== RESET & BASE STYLES ===== */
.admin-page * {
  box-sizing: border-box !important;
}

/* Completely neutralize any theme effects */
html.theme-light .admin-page *,
html.theme-dark .admin-page *,
html.light .admin-page *,
html.dark .admin-page *,
.theme-light .admin-page *,
.theme-dark .admin-page *,
.light .admin-page *,
.dark .admin-page *,
.admin-page * {
  /* Reset theme-specific properties */
  filter: none !important;
  backdrop-filter: none !important;
  color: inherit !important;
  background-color: inherit !important;
}

/* ===== ICON COLOR EXCEPTIONS ===== */
/* Allow FontAwesome icons and SVG icons to keep their specific theme colors */
.admin-page i.fas,
.admin-page i.far,
.admin-page i.fab {
  color: inherit !important;
}

/* Support for Heroicons (SVG icons) used in admin components */
.admin-page svg.text-blue-500 { color: #3b82f6 !important; }
.admin-page svg.text-green-500 { color: #10b981 !important; }
.admin-page svg.text-purple-500 { color: #8b5cf6 !important; }
.admin-page svg.text-orange-500 { color: #f97316 !important; }
.admin-page svg.text-red-500 { color: #ef4444 !important; }
.admin-page svg.text-indigo-500 { color: #6366f1 !important; }
.admin-page svg.text-gray-500 { color: #6b7280 !important; }
.admin-page svg.text-yellow-500 { color: #eab308 !important; }
.admin-page svg.text-teal-500 { color: #14b8a6 !important; }
.admin-page svg.text-pink-500 { color: #ec4899 !important; }

/* Specific color overrides for all admin section icons */
/* Primary colors (500 series) */
.admin-page i.text-blue-500 { color: #3b82f6 !important; }
.admin-page i.text-green-500 { color: #10b981 !important; }
.admin-page i.text-purple-500 { color: #8b5cf6 !important; }
.admin-page i.text-orange-500 { color: #f97316 !important; }
.admin-page i.text-red-500 { color: #ef4444 !important; }
.admin-page i.text-indigo-500 { color: #6366f1 !important; }
.admin-page i.text-gray-500 { color: #6b7280 !important; }
.admin-page i.text-yellow-500 { color: #eab308 !important; }
.admin-page i.text-teal-500 { color: #14b8a6 !important; }
.admin-page i.text-pink-500 { color: #ec4899 !important; }

/* Additional color variations used in services, options, and features */
.admin-page i.text-blue-400 { color: #60a5fa !important; }
.admin-page i.text-blue-600 { color: #2563eb !important; }
.admin-page i.text-green-600 { color: #059669 !important; }
.admin-page i.text-gray-600 { color: #4b5563 !important; }
.admin-page i.text-yellow-400 { color: #facc15 !important; }
.admin-page i.text-cyan-500 { color: #06b6d4 !important; }
.admin-page i.text-lime-500 { color: #84cc16 !important; }
.admin-page i.text-emerald-500 { color: #10b981 !important; }
.admin-page i.text-violet-500 { color: #8b5cf6 !important; }
.admin-page i.text-rose-500 { color: #f43f5e !important; }
.admin-page i.text-amber-500 { color: #f59e0b !important; }
.admin-page i.text-sky-500 { color: #0ea5e9 !important; }

/* Ensure admin page fills the entire viewport */
.admin-page {
  position: relative !important;
  min-height: 100vh !important;
  background-color: #f9fafb !important;
}

/* Force admin layout to ignore any theme styling */
html.theme-light .admin-page,
html.theme-dark .admin-page,
html.light .admin-page,
html.dark .admin-page,
body.theme-light .admin-page,
body.theme-dark .admin-page,
body.light .admin-page,
body.dark .admin-page {
  background-color: #f9fafb !important;
  color: #111827 !important;
}

/* Ensure body doesn't interfere with admin pages when theme classes are present */
html.theme-light body:has(.admin-page),
html.theme-dark body:has(.admin-page),
html.light body:has(.admin-page),
html.dark body:has(.admin-page),
body.theme-light:has(.admin-page),
body.theme-dark:has(.admin-page),
body.light:has(.admin-page),
body.dark:has(.admin-page) {
  background-color: #f9fafb !important;
  color: #111827 !important;
}

/* ===== DISABLE CUSTOM CURSOR AND ANIMATIONS ===== */
/* Completely disable custom cursor on admin pages */
.admin-page .cb-cursor,
.admin-page .cb-cursor-text,
.cb-cursor,
.cb-cursor-text {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Force default cursor on admin pages */
.admin-page,
.admin-page *,
.admin-page *:hover,
.admin-page *:active,
.admin-page *:focus {
  cursor: default !important;
}

/* Override any cursor styles that might be applied */
.admin-page a,
.admin-page button,
.admin-page input,
.admin-page textarea,
.admin-page select {
  cursor: pointer !important;
}

.admin-page input[type="text"],
.admin-page input[type="email"],
.admin-page input[type="password"],
.admin-page input[type="number"],
.admin-page textarea {
  cursor: text !important;
}

/* Disable all custom animations and transitions on admin pages */
.admin-page *,
.admin-page *::before,
.admin-page *::after {
  animation: none !important;
  transition: none !important;
  transform: none !important;
  will-change: auto !important;
}

/* Allow only essential transitions for UI feedback */
.admin-page button,
.admin-page input,
.admin-page textarea,
.admin-page select {
  transition: background-color 0.15s ease, border-color 0.15s ease, color 0.15s ease !important;
}

/* Disable any barslide or slide animations */
.admin-page .barslide,
.admin-page .slide,
.admin-page .swiper,
.admin-page .parallax,
.admin-page .wow,
.admin-page .reveal,
.admin-page .image-anime {
  animation: none !important;
  transform: none !important;
  transition: none !important;
}

/* Override any global cursor CSS that might be loaded */
body:has(.admin-page) .cb-cursor,
body:has(.admin-page) .cb-cursor-text,
html:has(.admin-page) .cb-cursor,
html:has(.admin-page) .cb-cursor-text {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Ensure no GSAP or other animation libraries affect admin pages */
.admin-page [data-cursor],
.admin-page [data-cursor-text],
.admin-page [data-aos],
.admin-page [data-wow],
.admin-page [data-parallax] {
  animation: none !important;
  transform: none !important;
  transition: none !important;
}

/* ===== SIMPLE SCROLLBAR STYLES FOR ADMIN PAGES ===== */
/* Override any complex scrollbar styles with simple ones */
.admin-page ::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.admin-page ::-webkit-scrollbar-track {
  background: #f3f4f6 !important;
  border-radius: 0 !important;
}

.admin-page ::-webkit-scrollbar-thumb {
  background: #d1d5db !important;
  border-radius: 0 !important;
  border: none !important;
}

.admin-page ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af !important;
}

.admin-page ::-webkit-scrollbar-corner {
  background: #f3f4f6 !important;
}

/* Firefox scrollbar for admin pages */
.admin-page * {
  scrollbar-width: thin !important;
  scrollbar-color: #d1d5db #f3f4f6 !important;
}

/* Remove any custom scrollbar classes that might be applied */
.admin-page .scrollbar-thin,
.admin-page .scrollbar-thick,
.admin-page .scrollbar-none {
  scrollbar-width: thin !important;
  scrollbar-color: #d1d5db #f3f4f6 !important;
}

.admin-page .scrollbar-thin::-webkit-scrollbar,
.admin-page .scrollbar-thick::-webkit-scrollbar,
.admin-page .scrollbar-none::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.admin-page .scrollbar-thin::-webkit-scrollbar-track,
.admin-page .scrollbar-thick::-webkit-scrollbar-track,
.admin-page .scrollbar-none::-webkit-scrollbar-track {
  background: #f3f4f6 !important;
  border-radius: 0 !important;
}

.admin-page .scrollbar-thin::-webkit-scrollbar-thumb,
.admin-page .scrollbar-thick::-webkit-scrollbar-thumb,
.admin-page .scrollbar-none::-webkit-scrollbar-thumb {
  background: #d1d5db !important;
  border-radius: 0 !important;
  border: none !important;
}

.admin-page .scrollbar-thin::-webkit-scrollbar-thumb:hover,
.admin-page .scrollbar-thick::-webkit-scrollbar-thumb:hover,
.admin-page .scrollbar-none::-webkit-scrollbar-thumb:hover {
  background: #9ca3af !important;
}

/* Main content area should fill remaining space */
.admin-page .lg\:pl-64 {
  min-height: calc(100vh - 0px);
  background-color: #f9fafb;
}

/* Ensure the main content area has proper background */
.admin-page main {
  background-color: #f9fafb;
  min-height: calc(100vh - 64px); /* Subtract header height */
}

/* ===== FORM ELEMENTS ===== */

/* Input Fields */
.admin-page input[type="text"],
.admin-page input[type="email"],
.admin-page input[type="password"],
.admin-page input[type="url"],
.admin-page input[type="number"],
.admin-page input[type="search"],
.admin-page input[type="tel"],
.admin-page input[type="date"],
.admin-page input[type="time"],
.admin-page input[type="datetime-local"],
.admin-page input[type="month"],
.admin-page input[type="week"],
.admin-page textarea,
.admin-page select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: white !important;
  color: #333 !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  width: 100% !important;
  transition: all 0.2s ease !important;
  outline: none !important;
}

/* Input Focus States */
.admin-page input[type="text"]:focus,
.admin-page input[type="email"]:focus,
.admin-page input[type="password"]:focus,
.admin-page input[type="url"]:focus,
.admin-page input[type="number"]:focus,
.admin-page input[type="search"]:focus,
.admin-page input[type="tel"]:focus,
.admin-page input[type="date"]:focus,
.admin-page input[type="time"]:focus,
.admin-page input[type="datetime-local"]:focus,
.admin-page input[type="month"]:focus,
.admin-page input[type="week"]:focus,
.admin-page textarea:focus,
.admin-page select:focus {
  border-color: #2563eb !important;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
  background-color: white !important;
}

/* Input Placeholders */
.admin-page input::placeholder,
.admin-page textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}

/* Search Input Specific Styling */
.admin-page input[type="search"],
.admin-page input[type="text"][placeholder*="Search"],
.admin-page .relative input[type="text"] {
  padding-left: 40px !important; /* Space for search icon */
  padding-right: 12px !important;
}

/* Search container with icon */
.admin-page .search-container {
  position: relative !important;
}

.admin-page .search-container .search-icon {
  position: absolute !important;
  left: 12px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #9ca3af !important;
  pointer-events: none !important;
  z-index: 10 !important;
}

.admin-page .search-container input[type="search"] {
  padding-left: 40px !important;
}

/* AdminHeader specific search styling */
.admin-page .relative.group input[type="text"] {
  padding-left: 40px !important;
  padding-right: 40px !important;
}

.admin-page .relative.group .absolute.left-3 {
  left: 12px !important;
  z-index: 10 !important;
}

.admin-page .relative.group .absolute.right-3 {
  right: 12px !important;
  z-index: 10 !important;
}

/* ===== CHECKBOXES ===== */
.admin-page input[type="checkbox"] {
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  width: 16px !important;
  height: 16px !important;
  border: 2px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: white !important;
  cursor: pointer !important;
  position: relative !important;
  vertical-align: middle !important;
  margin: 0 !important;
  outline: none !important;
  transition: all 0.2s ease !important;
}

.admin-page input[type="checkbox"]:checked {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e") !important;
  background-size: 12px 12px !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

.admin-page input[type="checkbox"]:focus {
  border-color: #2563eb !important;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

.admin-page input[type="checkbox"]:hover {
  border-color: #9ca3af !important;
}

/* ===== RADIO BUTTONS ===== */
.admin-page input[type="radio"] {
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  width: 16px !important;
  height: 16px !important;
  border: 2px solid #d1d5db !important;
  border-radius: 50% !important;
  background-color: white !important;
  cursor: pointer !important;
  position: relative !important;
  vertical-align: middle !important;
  margin: 0 !important;
  outline: none !important;
  transition: all 0.2s ease !important;
}

.admin-page input[type="radio"]:checked {
  border-color: #2563eb !important;
  background-color: white !important;
}

.admin-page input[type="radio"]:checked::after {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 6px !important;
  height: 6px !important;
  border-radius: 50% !important;
  background-color: #2563eb !important;
}

.admin-page input[type="radio"]:focus {
  border-color: #2563eb !important;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* ===== SELECT DROPDOWNS ===== */
.admin-page select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
  background-position: right 8px center !important;
  background-repeat: no-repeat !important;
  background-size: 16px 16px !important;
  padding-right: 32px !important;
}

.admin-page select option {
  background-color: white !important;
  color: #333 !important;
  padding: 8px 12px !important;
}

/* ===== BUTTONS ===== */
.admin-page button {
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  border: none !important;
  background: none !important;
  font-family: inherit !important;
  font-size: inherit !important;
  cursor: pointer !important;
  outline: none !important;
  transition: all 0.2s ease !important;
}

/* Modal Button Overrides - Allow Modal buttons to use their own styles */
.admin-page button.bg-blue-100,
.admin-page button.bg-gray-200,
.admin-page button.bg-gray-100,
.admin-page button.bg-red-100,
.admin-page button.bg-green-100,
.admin-page button.bg-orange-100 {
  background-color: inherit !important;
  border: inherit !important;
  color: inherit !important;
}

/* Text color overrides for Modal buttons */
.admin-page button.text-blue-800,
.admin-page button.text-gray-800,
.admin-page button.text-red-800,
.admin-page button.text-green-800,
.admin-page button.text-orange-800 {
  color: inherit !important;
}

/* Border overrides for Modal buttons */
.admin-page button.border-blue-200,
.admin-page button.border-gray-200,
.admin-page button.border-gray-300,
.admin-page button.border-red-200,
.admin-page button.border-green-200,
.admin-page button.border-orange-200 {
  border: inherit !important;
}

.admin-page button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* Primary Button */
.admin-page .btn-primary {
  background-color: #2563eb !important;
  color: rgb(95, 51, 51) !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  border: 1px solid #2563eb !important;
}

.admin-page .btn-primary:hover:not(:disabled) {
  background-color: #1d4ed8 !important;
  border-color: #1d4ed8 !important;
}

.admin-page .btn-primary:focus {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* Secondary Button */
.admin-page .btn-secondary {
  background-color: rgb(68, 33, 33) !important;
  color: #374151 !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  border: 1px solid #214377 !important;
}

.admin-page .btn-secondary:hover:not(:disabled) {
  background-color: #f9fafb !important;
  border-color: #9ca3af !important;
}

.admin-page .btn-secondary:focus {
  box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.1) !important;
}

/* Danger Button */
.admin-page .btn-danger {
  background-color: #dc2626 !important;
  color: rgb(170, 88, 88) !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  border: 1px solid #dc2626 !important;
}

.admin-page .btn-danger:hover:not(:disabled) {
  background-color: #b91c1c !important;
  border-color: #b91c1c !important;
}

.admin-page .btn-danger:focus {
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

/* ===== TABLES ===== */
.admin-page table {
  border-collapse: collapse !important;
  width: 100% !important;
  background-color: white !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.admin-page th {
  background-color: #9ca3af !important;
  color: #000000 !important;
  font-weight: 600 !important;
  text-align: left !important;
  padding: 8px 16px !important;
  border-bottom: 1px solid #e5e7eb !important;
  font-size: 12px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
}

.admin-page td {
  padding: 12px 16px !important;
  border-bottom: 1px solid #f3f4f6 !important;
  color: #374151 !important;
  font-size: 14px !important;
}

/* ===== LIST VIEW HEADERS ===== */
/* Style for list view headers that use div elements instead of th - be more specific */
.admin-page .bg-gray-50.border.border-gray-200.rounded-none.px-4.py-3.mb-2 {
  background-color: #9ca3af !important;
  padding: 8px 16px !important;
}

.admin-page .bg-gray-50.border.border-gray-200.rounded-none.px-4.py-3.mb-2 span {
  color: #000000 !important;
}

/* ===== ADDITIONAL HEADER STYLING ===== */
/* Ensure all admin table headers have consistent styling regardless of specific classes */
.admin-page thead {
  background-color: #9ca3af !important;
}

.admin-page thead th {
  background-color: #9ca3af !important;
  color: #000000 !important;
  padding: 8px 16px !important;
}

.admin-page tr:hover {
  background-color: #f9fafb !important;
}

/* ===== SECTION HEADER STYLING ===== */
/* Make section headers more compact with section-specific theme colors */
.admin-page .bg-white.rounded-lg.shadow-sm.border.border-gray-200\/50 {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* Base header styling */
.admin-page .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 {
  border: none !important;
  padding: 12px 16px !important;
  border-radius: 4px !important;
}

/* Categories Section - Light Blue Background */
.admin-page [data-section="categories"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50,
.admin-page .categories-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 {
  background: #dbeafe !important; /* blue-100 */
}

.admin-page [data-section="categories"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2,
.admin-page .categories-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2 {
  color: #1e40af !important; /* blue-800 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="categories"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p,
.admin-page .categories-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p {
  color: #1e3a8a !important; /* blue-900 */
  font-size: 13px !important;
  margin: 0 !important;
}

/* Services Section - Light Green Background */
.admin-page [data-section="services"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50,
.admin-page .services-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 {
  background: #d1fae5 !important; /* emerald-100 */
}

.admin-page [data-section="services"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2,
.admin-page .services-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2 {
  color: #065f46 !important; /* emerald-800 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="services"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p,
.admin-page .services-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p {
  color: #064e3b !important; /* emerald-900 */
  font-size: 13px !important;
  margin: 0 !important;
}

/* Options Section - Light Amber Background */
.admin-page [data-section="options"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50,
.admin-page .options-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 {
  background: #fef3c7 !important; /* amber-100 */
}

.admin-page [data-section="options"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2,
.admin-page .options-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2 {
  color: #92400e !important; /* amber-800 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="options"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p,
.admin-page .options-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p {
  color: #78350f !important; /* amber-900 */
  font-size: 13px !important;
  margin: 0 !important;
}

/* Features Section - Light Purple Background */
.admin-page [data-section="features"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50,
.admin-page .features-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 {
  background: #ede9fe !important; /* purple-100 */
}

.admin-page [data-section="features"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2,
.admin-page .features-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2 {
  color: #5b21b6 !important; /* purple-800 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="features"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p,
.admin-page .features-header .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p {
  color: #4c1d95 !important; /* purple-900 */
  font-size: 13px !important;
  margin: 0 !important;
}

/* ===== SERVICE MANAGEMENT SECTIONS STYLING ===== */
/* All sections now use CategoryHeader component with consistent styling */

/* Services Section Headers - Light Green Background */
.admin-page [data-section="services"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 {
  background: #d1fae5 !important; /* emerald-100 */
  border: none !important;
  padding: 12px 16px !important;
  border-radius: 4px !important;
}

.admin-page [data-section="services"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2 {
  color: #065f46 !important; /* emerald-800 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="services"] .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p {
  color: #064e3b !important; /* emerald-900 */
  font-size: 13px !important;
  margin: 0 !important;
}

/* Service Options Section Headers - Light Amber Background */
.admin-page [data-section="options"] .space-y-6 > div:first-child .bg-white.rounded-lg.shadow-sm.border.border-gray-200\/50 {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.admin-page [data-section="options"] .space-y-6 > div:first-child .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 {
  background: #fef3c7 !important; /* amber-100 */
  border: none !important;
  padding: 12px 16px !important;
  border-radius: 4px !important;
}

.admin-page [data-section="options"] .space-y-6 > div:first-child .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2 {
  color: #92400e !important; /* amber-800 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="options"] .space-y-6 > div:first-child .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p {
  color: #78350f !important; /* amber-900 */
  font-size: 13px !important;
  margin: 0 !important;
}

/* Option Features Section Headers - Light Purple Background */
.admin-page [data-section="features"] .space-y-6 > div:first-child .bg-white.rounded-lg.shadow-sm.border.border-gray-200\/50 {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.admin-page [data-section="features"] .space-y-6 > div:first-child .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 {
  background: #ede9fe !important; /* purple-100 */
  border: none !important;
  padding: 12px 16px !important;
  border-radius: 4px !important;
}

.admin-page [data-section="features"] .space-y-6 > div:first-child .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 h2 {
  color: #5b21b6 !important; /* purple-800 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="features"] .space-y-6 > div:first-child .bg-gradient-to-r.from-gray-50.to-white.border-b.border-gray-200\/50 p {
  color: #4c1d95 !important; /* purple-900 */
  font-size: 13px !important;
  margin: 0 !important;
}

/* ===== ADMIN HEADER STYLING FOR OTHER SECTIONS ===== */
/* Base styling for AdminHeader component */
.admin-page .bg-white.rounded-xl.border.border-gray-200.shadow-sm {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.admin-page .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  padding: 12px 16px !important;
  background-color: #f3f4f6 !important;
  border-radius: 4px !important;
}

/* Blog Posts Section - Light Orange Background */
.admin-page [data-section="blog"] h1,
.admin-page .blog-header h1,
body[data-pathname*="/blog"] .admin-page h1 {
  color: #ea580c !important; /* orange-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="blog"] p,
.admin-page .blog-header p,
body[data-pathname*="/blog"] .admin-page p {
  color: #c2410c !important; /* orange-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="blog"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .blog-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
body[data-pathname*="/blog"] .admin-page .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #fed7aa !important; /* orange-200 */
}

/* Team Members Section - Light Indigo Background */
.admin-page [data-section="team-members"] h1,
.admin-page .team-members-header h1 {
  color: #4338ca !important; /* indigo-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="team-members"] p,
.admin-page .team-members-header p {
  color: #3730a3 !important; /* indigo-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="team-members"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .team-members-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #e0e7ff !important; /* indigo-200 */
}

/* Technologies Section - Light Cyan Background */
.admin-page [data-section="technologies"] h1,
.admin-page .technologies-header h1 {
  color: #0891b2 !important; /* cyan-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="technologies"] p,
.admin-page .technologies-header p {
  color: #0e7490 !important; /* cyan-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="technologies"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .technologies-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #cffafe !important; /* cyan-200 */
}

/* Testimonials Section - Light Pink Background */
.admin-page [data-section="testimonials"] h1,
.admin-page .testimonials-header h1 {
  color: #db2777 !important; /* pink-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="testimonials"] p,
.admin-page .testimonials-header p {
  color: #be185d !important; /* pink-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="testimonials"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .testimonials-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #fce7f3 !important; /* pink-200 */
}

/* Legal Pages Section - Light Gray Background */
.admin-page [data-section="legal-pages"] h1,
.admin-page .legal-pages-header h1 {
  color: #4b5563 !important; /* gray-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="legal-pages"] p,
.admin-page .legal-pages-header p {
  color: #374151 !important; /* gray-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="legal-pages"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .legal-pages-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #e5e7eb !important; /* gray-200 */
}

/* Jobs Section - Light Emerald Background */
.admin-page [data-section="jobs"] h1,
.admin-page .jobs-header h1 {
  color: #059669 !important; /* emerald-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="jobs"] p,
.admin-page .jobs-header p {
  color: #047857 !important; /* emerald-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="jobs"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .jobs-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #d1fae5 !important; /* emerald-200 */
}

/* Clients Section - Light Blue Background */
.admin-page [data-section="clients"] h1,
.admin-page .clients-header h1 {
  color: #2563eb !important; /* blue-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="clients"] p,
.admin-page .clients-header p {
  color: #1d4ed8 !important; /* blue-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="clients"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .clients-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #dbeafe !important; /* blue-200 */
}

/* Projects Section - Light Violet Background */
.admin-page [data-section="projects"] h1,
.admin-page .projects-header h1 {
  color: #7c3aed !important; /* violet-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="projects"] p,
.admin-page .projects-header p {
  color: #6d28d9 !important; /* violet-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="projects"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .projects-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #ede9fe !important; /* violet-200 */
}

/* Invoices Section - Light Yellow Background */
.admin-page [data-section="invoices"] h1,
.admin-page .invoices-header h1 {
  color: #ca8a04 !important; /* yellow-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="invoices"] p,
.admin-page .invoices-header p {
  color: #a16207 !important; /* yellow-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="invoices"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .invoices-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #fef3c7 !important; /* yellow-200 */
}

/* Contact Forms Section - Light Teal Background */
.admin-page [data-section="contact-forms"] h1,
.admin-page .contact-forms-header h1 {
  color: #0d9488 !important; /* teal-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="contact-forms"] p,
.admin-page .contact-forms-header p {
  color: #0f766e !important; /* teal-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="contact-forms"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .contact-forms-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #ccfbf1 !important; /* teal-200 */
}

/* Users Section - Light Red Background */
.admin-page [data-section="users"] h1,
.admin-page .users-header h1 {
  color: #dc2626 !important; /* red-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="users"] p,
.admin-page .users-header p {
  color: #b91c1c !important; /* red-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="users"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .users-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #fecaca !important; /* red-200 */
}

/* Settings Section - Light Slate Background */
.admin-page [data-section="settings"] h1,
.admin-page .settings-header h1 {
  color: #475569 !important; /* slate-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

.admin-page [data-section="settings"] p,
.admin-page .settings-header p {
  color: #334155 !important; /* slate-700 */
  font-size: 13px !important;
  margin: 0 !important;
}

.admin-page [data-section="settings"] .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6,
.admin-page .settings-header .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #e2e8f0 !important; /* slate-200 */
}

/* ===== AUTO-DETECTION BASED ON URL ===== */
/* This will automatically apply colors based on the current URL path */

/* Blog section auto-detection */
body[data-current-section="blog"] .admin-page h1 {
  color: #ea580c !important; /* orange-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

body[data-current-section="blog"] .admin-page .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #fed7aa !important; /* orange-200 */
}

/* Team Members section auto-detection */
body[data-current-section="team-members"] .admin-page h1 {
  color: #4338ca !important; /* indigo-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

body[data-current-section="team-members"] .admin-page .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #e0e7ff !important; /* indigo-200 */
}

/* Technologies section auto-detection */
body[data-current-section="technologies"] .admin-page h1 {
  color: #0891b2 !important; /* cyan-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

body[data-current-section="technologies"] .admin-page .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #cffafe !important; /* cyan-200 */
}

/* Testimonials section auto-detection */
body[data-current-section="testimonials"] .admin-page h1 {
  color: #db2777 !important; /* pink-600 */
  font-size: 16px !important;
  margin: 0 !important;
}

body[data-current-section="testimonials"] .admin-page .bg-white.rounded-xl.border.border-gray-200.shadow-sm .p-6 {
  background-color: #fce7f3 !important; /* pink-200 */
}

/* ===== MODALS ===== */
.admin-page [role="dialog"] {
  background-color: white !important;
  border-radius: 8px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #e5e7eb !important;
}

.admin-page [role="dialog"] input,
.admin-page [role="dialog"] textarea,
.admin-page [role="dialog"] select {
  background-color: white !important;
  color: #333 !important;
  border: 1px solid #d1d5db !important;
}

/* ===== CARDS ===== */
.admin-page .card {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
}

/* ===== STAT CARDS SPECIFIC STYLING ===== */
/* Allow StatCards to use their theme-specific backgrounds */
.admin-page .bg-blue-50.shadow-sm.rounded-lg { background-color: #eff6ff !important; }
.admin-page .bg-emerald-50.shadow-sm.rounded-lg { background-color: #ecfdf5 !important; }
.admin-page .bg-amber-50.shadow-sm.rounded-lg { background-color: #fffbeb !important; }
.admin-page .bg-purple-50.shadow-sm.rounded-lg { background-color: #faf5ff !important; }
.admin-page .bg-indigo-50.shadow-sm.rounded-lg { background-color: #eef2ff !important; }
.admin-page .bg-pink-50.shadow-sm.rounded-lg { background-color: #fdf2f8 !important; }

/* Ensure StatCard icons maintain their size and colors */
.admin-page .h-10.w-10 {
  height: 2.5rem !important;
  width: 2.5rem !important;
}

/* Ensure StatCard text sizes are maintained */
.admin-page .text-2xl.font-bold {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  line-height: 2rem !important;
}

/* ===== BADGES ===== */
.admin-page .badge {
  display: inline-flex !important;
  align-items: center !important;
  padding: 4px 8px !important;
  border-radius: 9999px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  line-height: 1 !important;
}

.admin-page .badge-success {
  background-color: #dcfce7 !important;
  color: #166534 !important;
}

.admin-page .badge-warning {
  background-color: #fef3c7 !important;
  color: #92400e !important;
}

.admin-page .badge-danger {
  background-color: #fee2e2 !important;
  color: #991b1b !important;
}

.admin-page .badge-info {
  background-color: #dbeafe !important;
  color: #1e40af !important;
}

/* ===== UTILITY CLASSES ===== */
.admin-page .text-primary { color: #2563eb !important; }
.admin-page .text-secondary { color: #6b7280 !important; }
.admin-page .text-success { color: #059669 !important; }
.admin-page .text-warning { color: #d97706 !important; }
.admin-page .text-danger { color: #dc2626 !important; }

.admin-page .bg-white { background-color: white !important; }
.admin-page .bg-gray-50 { background-color: #f9fafb !important; }
.admin-page .bg-gray-100 { background-color: #f3f4f6 !important; }

/* ===== STAT CARD THEME BACKGROUNDS ===== */
.admin-page .bg-blue-50 { background-color: #eff6ff !important; }
.admin-page .bg-emerald-50 { background-color: #ecfdf5 !important; }
.admin-page .bg-green-50 { background-color: #f0fdf4 !important; }
.admin-page .bg-amber-50 { background-color: #fffbeb !important; }
.admin-page .bg-yellow-50 { background-color: #fffbeb !important; }
.admin-page .bg-purple-50 { background-color: #faf5ff !important; }
.admin-page .bg-indigo-50 { background-color: #eef2ff !important; }
.admin-page .bg-pink-50 { background-color: #fdf2f8 !important; }
.admin-page .bg-red-50 { background-color: #fef2f2 !important; }

/* ===== SECTION CARD ULTRA-LIGHT BACKGROUNDS ===== */
.admin-page .bg-blue-25 { background-color: #f7faff !important; }
.admin-page .bg-emerald-25 { background-color: #f6fefb !important; }
.admin-page .bg-amber-25 { background-color: #fffef5 !important; }
.admin-page .bg-purple-25 { background-color: #fdfaff !important; }
.admin-page .bg-gray-25 { background-color: #fcfcfd !important; }

/* ===== STAT CARD TEXT COLORS ===== */
.admin-page .text-blue-600 { color: #2563eb !important; }
.admin-page .text-blue-800 { color: #1e40af !important; }
.admin-page .text-blue-900 { color: #1e3a8a !important; }

.admin-page .text-emerald-600 { color: #059669 !important; }
.admin-page .text-emerald-800 { color: #065f46 !important; }
.admin-page .text-emerald-900 { color: #064e3b !important; }

.admin-page .text-amber-600 { color: #d97706 !important; }
.admin-page .text-amber-800 { color: #92400e !important; }
.admin-page .text-amber-900 { color: #78350f !important; }

.admin-page .text-purple-600 { color: #9333ea !important; }
.admin-page .text-purple-800 { color: #6b21a8 !important; }
.admin-page .text-purple-900 { color: #581c87 !important; }

.admin-page .text-indigo-600 { color: #4f46e5 !important; }
.admin-page .text-indigo-800 { color: #3730a3 !important; }
.admin-page .text-indigo-900 { color: #312e81 !important; }

.admin-page .text-pink-600 { color: #db2777 !important; }
.admin-page .text-pink-800 { color: #9d174d !important; }
.admin-page .text-pink-900 { color: #831843 !important; }

/* ===== STAT CARD OVERRIDES ===== */
/* Ensure StatCard styling takes precedence over general admin styles */
.admin-page div[class*="bg-blue-50"][class*="shadow-sm"] {
  background-color: #eff6ff !important;
}

.admin-page div[class*="bg-emerald-50"][class*="shadow-sm"] {
  background-color: #ecfdf5 !important;
}

.admin-page div[class*="bg-amber-50"][class*="shadow-sm"] {
  background-color: #fffbeb !important;
}

.admin-page div[class*="bg-purple-50"][class*="shadow-sm"] {
  background-color: #faf5ff !important;
}

.admin-page div[class*="bg-indigo-50"][class*="shadow-sm"] {
  background-color: #eef2ff !important;
}

.admin-page div[class*="bg-pink-50"][class*="shadow-sm"] {
  background-color: #fdf2f8 !important;
}

/* Ensure StatCard text colors work properly */
.admin-page div[class*="shadow-sm"] .text-blue-600 { color: #2563eb !important; }
.admin-page div[class*="shadow-sm"] .text-blue-800 { color: #1e40af !important; }
.admin-page div[class*="shadow-sm"] .text-blue-900 { color: #1e3a8a !important; }

.admin-page div[class*="shadow-sm"] .text-emerald-600 { color: #059669 !important; }
.admin-page div[class*="shadow-sm"] .text-emerald-800 { color: #065f46 !important; }
.admin-page div[class*="shadow-sm"] .text-emerald-900 { color: #064e3b !important; }

.admin-page div[class*="shadow-sm"] .text-amber-600 { color: #d97706 !important; }
.admin-page div[class*="shadow-sm"] .text-amber-800 { color: #92400e !important; }
.admin-page div[class*="shadow-sm"] .text-amber-900 { color: #78350f !important; }

.admin-page div[class*="shadow-sm"] .text-purple-600 { color: #9333ea !important; }
.admin-page div[class*="shadow-sm"] .text-purple-800 { color: #6b21a8 !important; }
.admin-page div[class*="shadow-sm"] .text-purple-900 { color: #581c87 !important; }

.admin-page div[class*="shadow-sm"] .text-indigo-600 { color: #4f46e5 !important; }
.admin-page div[class*="shadow-sm"] .text-indigo-800 { color: #3730a3 !important; }
.admin-page div[class*="shadow-sm"] .text-indigo-900 { color: #312e81 !important; }

.admin-page div[class*="shadow-sm"] .text-pink-600 { color: #db2777 !important; }
.admin-page div[class*="shadow-sm"] .text-pink-800 { color: #9d174d !important; }
.admin-page div[class*="shadow-sm"] .text-pink-900 { color: #831843 !important; }

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .admin-page input[type="text"],
  .admin-page input[type="email"],
  .admin-page input[type="password"],
  .admin-page input[type="url"],
  .admin-page input[type="number"],
  .admin-page input[type="search"],
  .admin-page input[type="tel"],
  .admin-page input[type="date"],
  .admin-page input[type="time"],
  .admin-page input[type="datetime-local"],
  .admin-page input[type="month"],
  .admin-page input[type="week"],
  .admin-page textarea,
  .admin-page select {
    font-size: 16px !important; /* Prevents zoom on iOS */
  }
}

/* ===== ACCESSIBILITY ===== */
.admin-page input:focus,
.admin-page textarea:focus,
.admin-page select:focus,
.admin-page button:focus {
  outline: 2px solid #2563eb !important;
  outline-offset: 2px !important;
}

/* ===== PRINT STYLES ===== */
@media print {
  .admin-page {
    background-color: white !important;
    color: black !important;
  }
  
  .admin-page input,
  .admin-page textarea,
  .admin-page select {
    border: 1px solid #000 !important;
    background-color: white !important;
    color: black !important;
  }
}
